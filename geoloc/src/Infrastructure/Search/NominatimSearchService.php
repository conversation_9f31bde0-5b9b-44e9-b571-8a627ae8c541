<?php

declare(strict_types=1);

namespace App\Infrastructure\Search;

use App\Domain\Search\Search;
use Psr\Log\LoggerInterface;

class NominatimSearchService
{
    private $nominatimBaseUrl;

    public function __construct(
        private readonly LoggerInterface $logger
    ) {
        $this->nominatimBaseUrl = "http://nominatim:8080";
    }

    /**
     * Perform a free-form search query against Nominatim API
     *
     * @param string $query The search query
     * @param string $format Response format (json, jsonv2, xml, geojson, geocodejson)
     * @param int $limit Maximum number of results to return
     * @param bool $addressDetails Include address breakdown in results
     * @param bool $extraTags Include additional tags in results
     * @param bool $nameDetails Include name details in results
     * @return array Array of search results
     * @throws \Exception If the API request fails
     */
    public function freeFormSearch(
        string $query,
        string $format = 'jsonv2',
        int $limit = 10,
        bool $addressDetails = true,
        bool $extraTags = false,
        bool $nameDetails = false
    ): array {
        $url = $this->buildSearchUrl($query, $format, $limit, $addressDetails, $extraTags, $nameDetails);
        
        $this->logger->info('Making Nominatim API request', ['url' => $url]);

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERAGENT => 'Geoloc-App/1.0',
            CURLOPT_HTTPHEADER => [
                'Accept: application/json',
            ],
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($response === false || !empty($error)) {
            $this->logger->error('Nominatim API request failed', ['error' => $error]);
            throw new \Exception('Failed to connect to Nominatim API: ' . $error);
        }

        if ($httpCode !== 200) {
            $this->logger->error('Nominatim API returned error', ['http_code' => $httpCode, 'response' => $response]);
            throw new \Exception('Nominatim API returned HTTP ' . $httpCode);
        }

        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->logger->error('Failed to decode Nominatim API response', ['json_error' => json_last_error_msg()]);
            throw new \Exception('Invalid JSON response from Nominatim API');
        }

        $this->logger->info('Nominatim API request successful', ['result_count' => count($data)]);

        return $data;
    }

    private function buildSearchUrl(
        string $query,
        string $format,
        int $limit,
        bool $addressDetails,
        bool $extraTags,
        bool $nameDetails
    ): string {
        $params = [
            'q' => $query,
            'format' => $format,
            'limit' => $limit,
            'addressdetails' => $addressDetails ? '1' : '0',
            'extratags' => $extraTags ? '1' : '0',
            'namedetails' => $nameDetails ? '1' : '0',
        ];

        return $this->nominatimBaseUrl . '/search?' . http_build_query($params);
    }
}
