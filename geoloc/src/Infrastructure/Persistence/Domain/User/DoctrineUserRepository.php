<?php

namespace App\Infrastructure\Persistence\Domain\User;

use App\Domain\User\Exception\UserNotFoundException;
use App\Domain\User\User;
use App\Domain\User\UserRepository;
use Doctrine\DBAL\Connection;

class DoctrineUserRepository implements UserRepository
{
    public function __construct(
        private readonly Connection $db
    ) {
    }

    #[\Override]
    public function findAll(): array
    {
        $result = $this->db->createQueryBuilder()
            ->select('id_user', 'username', 'created_at', 'updated_at', 'role')
            ->from('user')
            ->executeQuery()
        ;

        $users = [];

        while ($data = $result->fetchAssociative()) {
            $users[] = new User(
                $data['id_user'],
                $data['username'],
                $data['created_at'] ? new \DateTime($data['created_at']) : null,
                $data['updated_at'] ? new \DateTime($data['updated_at']) : null,
                $data['role'] ?? 'user'
            );
        }

        return $users;
    }

    #[\Override]
    public function findUserOfId(int $id): User
    {
        $data = $this->db->createQueryBuilder()
            ->select('id_user', 'username', 'created_at', 'updated_at', 'role')
            ->from('user')
            ->where('id_user = :id')
            ->setParameter('id', $id)
            ->fetchAssociative()
        ;

        if (!$data) {
            throw new UserNotFoundException();
        }

        return new User(
            $data['id_user'],
            $data['username'],
            $data['created_at'] ? new \DateTime($data['created_at']) : null,
            $data['updated_at'] ? new \DateTime($data['updated_at']) : null,
            $data['role'] ?? 'user'
        );
    }

    public function findUserByUsername(string $username): User
    {
        $data = $this->db->createQueryBuilder()
            ->select('id_user', 'username', 'created_at', 'updated_at', 'role')
            ->from('user')
            ->where('username = :username')
            ->setParameter('username', $username)
            ->fetchAssociative()
        ;

        if (!$data) {
            throw new UserNotFoundException();
        }

        return new User(
            $data['id_user'],
            $data['username'],
            $data['created_at'] ? new \DateTime($data['created_at']) : null,
            $data['updated_at'] ? new \DateTime($data['updated_at']) : null,
            $data['role'] ?? 'user'
        );
    }

    public function getUserPassword(User $user): string
    {
        $data = $this->db->createQueryBuilder()
            ->select('password')
            ->from('user')
            ->where('id_user = :id')
            ->setParameter('id', $user->getId())
            ->fetchAssociative()
        ;

        return $data['password'] ?? '';
    }

    public function findUserByApiKey(string $apiKey): User
    {
        $data = $this->db->createQueryBuilder()
            ->select('u.id_user', 'u.username')
            ->from('user', 'u')
            ->join('u', 'api_key', 'ak', 'u.id_user = ak.id_user')
            ->where('ak.api_key = :key')
            ->setParameter('key', $apiKey)
            ->fetchAssociative()
        ;

        if (!$data) {
            throw new UserNotFoundException();
        }

        return new User($data['id_user'], $data['username']);
    }

    public function createUser(
        string $username,
        string $password
    ): User {
        $now = new \DateTime();

        $this->db->insert('user', [
            'username' => $username,
            'password' => $password,
            'created_at' => $now->format('Y-m-d H:i:s'),
            'updated_at' => $now->format('Y-m-d H:i:s'),
        ]);

        $id_user = (int) $this->db->lastInsertId();

        return new User($id_user, $username, $now, $now, 'user');
    }

    #[\Override]
    public function updateUser(int $id, string $username): User
    {
        // First verify user exists
        $this->findUserOfId($id);

        $now = new \DateTime();

        $this->db->update('user', [
            'username' => $username,
            'updated_at' => $now->format('Y-m-d H:i:s'),
        ], ['id_user' => $id]);

        return $this->findUserOfId($id);
    }

    #[\Override]
    public function deleteUser(int $id): void
    {
        // First verify user exists
        $this->findUserOfId($id);

        $this->db->delete('user', ['id_user' => $id]);
    }

    #[\Override]
    public function findUserByApiToken(string $token): User
    {
        $data = $this->db->createQueryBuilder()
            ->select('u.id_user', 'u.username', 'u.created_at', 'u.updated_at', 'u.role')
            ->from('user', 'u')
            ->innerJoin('u', 'api_tokens', 't', 'u.id_user = t.user_id')
            ->where('t.token = :token')
            ->andWhere('t.is_active = 1')
            ->andWhere('(t.expires_at IS NULL OR t.expires_at > NOW())')
            ->setParameter('token', $token)
            ->fetchAssociative()
        ;

        if (!$data) {
            throw new UserNotFoundException();
        }

        return new User(
            $data['id_user'],
            $data['username'],
            $data['created_at'] ? new \DateTime($data['created_at']) : null,
            $data['updated_at'] ? new \DateTime($data['updated_at']) : null,
            $data['role'] ?? 'user'
        );
    }
}
