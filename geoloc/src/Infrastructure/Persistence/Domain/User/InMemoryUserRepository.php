<?php

declare(strict_types=1);

namespace App\Infrastructure\Persistence\Domain\User;

use App\Domain\User\Exception\UserNotFoundException;
use App\Domain\User\User;
use App\Domain\User\UserRepository;

class InMemoryUserRepository implements UserRepository
{
    /**
     * @var User[]
     */
    private array $users;

    private array $apiKeys;

    /**
     * @param User[]|null $users
     */
    public function __construct(?array $users = null)
    {
        $now = new \DateTime();
        $this->users = $users ?? [
            1 => new User(1, 'bill.gates', $now, $now, 'admin'),
            2 => new User(2, 'steve.jobs', $now, $now, 'user'),
            3 => new User(3, 'mark.zuckerberg', $now, $now, 'user'),
            4 => new User(4, 'evan.spiegel', $now, $now, 'user'),
            5 => new User(5, 'jack.dorsey', $now, $now, 'user'),
        ];

        $this->apiKeys = [
            'abc123' => 1,
            'test789' => 4,
        ];
    }

    public function findAll(): array
    {
        return array_values($this->users);
    }

    public function findUserOfId(int $id): User
    {
        if (!isset($this->users[$id])) {
            throw new UserNotFoundException();
        }

        return $this->users[$id];
    }

    public function findUserByUsername(string $username): User
    {
        foreach ($this->users as $user) {
            if ($user->getUsername() === $username) {
                return $user;
            }
        }

        throw new UserNotFoundException();
    }

    public function getUserPassword(User $user): string
    {
        return password_hash($user->getUsername(), PASSWORD_BCRYPT);
    }

    public function findUserByApiKey(string $apiKey): User
    {
        if (!isset($this->apiKeys[$apiKey])) {
            throw new UserNotFoundException();
        }

        $id_user = $this->apiKeys[$apiKey];

        return $this->findUserOfId($id_user);
    }

    public function createUser(
        string $username,
        string $password
    ): User {
        $nextId = count($this->users) + 1;
        $now = new \DateTime();

        $this->users[$nextId] = new User($nextId, $username, $now, $now, 'user');

        return $this->users[$nextId];
    }

    public function updateUser(int $id, string $username): User
    {
        if (!isset($this->users[$id])) {
            throw new UserNotFoundException();
        }

        $this->users[$id] = $this->users[$id]->withUsername($username);

        return $this->users[$id];
    }

    public function deleteUser(int $id): void
    {
        if (!isset($this->users[$id])) {
            throw new UserNotFoundException();
        }

        unset($this->users[$id]);
    }

    public function findUserByApiToken(string $token): User
    {
        $userId = $this->apiKeys[$token] ?? null;

        if ($userId === null || !isset($this->users[$userId])) {
            throw new UserNotFoundException();
        }

        return $this->users[$userId];
    }
}
