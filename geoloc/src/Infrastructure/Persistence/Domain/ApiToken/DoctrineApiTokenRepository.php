<?php

declare(strict_types=1);

namespace App\Infrastructure\Persistence\Domain\ApiToken;

use App\Domain\ApiToken\ApiToken;
use App\Domain\ApiToken\ApiTokenRepository;
use App\Domain\ApiToken\Exception\ApiTokenNotFoundException;
use Doctrine\DBAL\Connection;

class DoctrineApiTokenRepository implements ApiTokenRepository
{
    public function __construct(
        private readonly Connection $db
    ) {
    }

    #[\Override]
    public function findAll(): array
    {
        $result = $this->db->createQueryBuilder()
            ->select('id', 'token', 'name', 'user_id', 'is_active', 'expires_at', 'created_at', 'updated_at')
            ->from('api_tokens')
            ->executeQuery()
        ;

        $tokens = [];

        while ($data = $result->fetchAssociative()) {
            $tokens[] = $this->createTokenFromData($data);
        }

        return $tokens;
    }

    #[\Override]
    public function findByUserId(int $userId): array
    {
        $result = $this->db->createQueryBuilder()
            ->select('id', 'token', 'name', 'user_id', 'is_active', 'expires_at', 'created_at', 'updated_at')
            ->from('api_tokens')
            ->where('user_id = :userId')
            ->setParameter('userId', $userId)
            ->executeQuery()
        ;

        $tokens = [];

        while ($data = $result->fetchAssociative()) {
            $tokens[] = $this->createTokenFromData($data);
        }

        return $tokens;
    }

    #[\Override]
    public function findTokenOfId(int $id): ApiToken
    {
        $data = $this->db->createQueryBuilder()
            ->select('id', 'token', 'name', 'user_id', 'is_active', 'expires_at', 'created_at', 'updated_at')
            ->from('api_tokens')
            ->where('id = :id')
            ->setParameter('id', $id)
            ->fetchAssociative()
        ;

        if (!$data) {
            throw new ApiTokenNotFoundException();
        }

        return $this->createTokenFromData($data);
    }

    #[\Override]
    public function findByToken(string $token): ApiToken
    {
        $data = $this->db->createQueryBuilder()
            ->select('id', 'token', 'name', 'user_id', 'is_active', 'expires_at', 'created_at', 'updated_at')
            ->from('api_tokens')
            ->where('token = :token')
            ->setParameter('token', $token)
            ->fetchAssociative()
        ;

        if (!$data) {
            throw new ApiTokenNotFoundException();
        }

        return $this->createTokenFromData($data);
    }

    #[\Override]
    public function findValidToken(string $token): ApiToken
    {
        $data = $this->db->createQueryBuilder()
            ->select('id', 'token', 'name', 'user_id', 'is_active', 'expires_at', 'created_at', 'updated_at')
            ->from('api_tokens')
            ->where('token = :token')
            ->andWhere('is_active = 1')
            ->andWhere('(expires_at IS NULL OR expires_at > NOW())')
            ->setParameter('token', $token)
            ->fetchAssociative()
        ;

        if (!$data) {
            throw new ApiTokenNotFoundException();
        }

        return $this->createTokenFromData($data);
    }

    #[\Override]
    public function createToken(string $name, int $userId, ?\DateTime $expiresAt = null): ApiToken
    {
        $now = new \DateTime();
        $token = $this->generateToken();

        $this->db->insert('api_tokens', [
            'token' => $token,
            'name' => $name,
            'user_id' => $userId,
            'is_active' => true,
            'expires_at' => $expiresAt?->format('Y-m-d H:i:s'),
            'created_at' => $now->format('Y-m-d H:i:s'),
            'updated_at' => $now->format('Y-m-d H:i:s'),
        ]);

        $id = (int) $this->db->lastInsertId();

        return new ApiToken($id, $token, $name, $userId, true, $expiresAt, $now, $now);
    }

    #[\Override]
    public function updateToken(int $id, string $name, bool $isActive): ApiToken
    {
        // First verify token exists
        $this->findTokenOfId($id);

        $now = new \DateTime();

        $this->db->update('api_tokens', [
            'name' => $name,
            'is_active' => $isActive,
            'updated_at' => $now->format('Y-m-d H:i:s'),
        ], ['id' => $id]);

        return $this->findTokenOfId($id);
    }

    #[\Override]
    public function deleteToken(int $id): void
    {
        // First verify token exists
        $this->findTokenOfId($id);

        $this->db->delete('api_tokens', ['id' => $id]);
    }

    private function createTokenFromData(array $data): ApiToken
    {
        return new ApiToken(
            $data['id'],
            $data['token'],
            $data['name'],
            $data['user_id'],
            (bool) $data['is_active'],
            $data['expires_at'] ? new \DateTime($data['expires_at']) : null,
            $data['created_at'] ? new \DateTime($data['created_at']) : null,
            $data['updated_at'] ? new \DateTime($data['updated_at']) : null
        );
    }

    private function generateToken(): string
    {
        return bin2hex(random_bytes(32));
    }
}
