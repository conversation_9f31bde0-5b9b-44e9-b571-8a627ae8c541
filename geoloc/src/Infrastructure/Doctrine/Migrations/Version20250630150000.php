<?php

declare(strict_types=1);

namespace App\Infrastructure\Doctrine\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Enhance api_tokens table and add user roles
 */
final class Version20250630150000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Enhance api_tokens table and add user roles';
    }

    public function up(Schema $schema): void
    {
        // Add role column to user table
        $userTable = $schema->getTable('user');
        if (!$userTable->hasColumn('role')) {
            $userTable->addColumn('role', 'string', ['length' => 50, 'default' => 'user']);
        }

        // Drop existing access_token table if it exists
        if ($schema->hasTable('access_token')) {
            $schema->dropTable('access_token');
        }

        // Create new api_tokens table
        $table = $schema->createTable('api_tokens');
        
        $table->addColumn('id', 'integer', ['unsigned' => true, 'autoincrement' => true]);
        $table->setPrimaryKey(['id']);
        
        $table->addColumn('token', 'string', ['length' => 255]);
        $table->addUniqueIndex(['token']);
        
        $table->addColumn('name', 'string', ['length' => 255]);
        
        $table->addColumn('user_id', 'integer', ['unsigned' => true]);
        $table->addForeignKeyConstraint('user', ['user_id'], ['id_user'], 
            ['onUpdate' => 'CASCADE', 'onDelete' => 'CASCADE']);
        
        $table->addColumn('is_active', 'boolean', ['default' => true]);
        
        $table->addColumn('expires_at', 'datetime', ['notnull' => false]);
        
        $table->addColumn('created_at', 'datetime', ['notnull' => false]);
        
        $table->addColumn('updated_at', 'datetime', ['notnull' => false]);
    }

    public function down(Schema $schema): void
    {
        // Remove role column from user table
        $userTable = $schema->getTable('user');
        if ($userTable->hasColumn('role')) {
            $userTable->dropColumn('role');
        }

        // Drop api_tokens table
        if ($schema->hasTable('api_tokens')) {
            $schema->dropTable('api_tokens');
        }

        // Recreate original access_token table
        $table = $schema->createTable('access_token');
        $table->addColumn('token', 'string', ['length' => 255]);
        $table->setPrimaryKey(['token']);
        $table->addColumn('id_user', 'integer', ['unsigned' => true, 'notnull' => false]);
        $table->addForeignKeyConstraint('user', ['id_user'], ['id_user'], 
            ['onUpdate' => 'CASCADE', 'onDelete' => 'CASCADE']);
        $table->addColumn('client', 'string', ['length' => 128]);
        $table->addColumn('date_expiration', 'datetime');
    }
}
