<?php

declare(strict_types=1);

namespace App\Infrastructure\Doctrine\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Add timestamp columns to user table
 */
final class Version20250630140000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add created_at and updated_at columns to user table';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('user');
        
        if (!$table->hasColumn('created_at')) {
            $table->addColumn('created_at', 'datetime', ['notnull' => false]);
        }
        
        if (!$table->hasColumn('updated_at')) {
            $table->addColumn('updated_at', 'datetime', ['notnull' => false]);
        }
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('user');
        
        if ($table->hasColumn('created_at')) {
            $table->dropColumn('created_at');
        }
        
        if ($table->hasColumn('updated_at')) {
            $table->dropColumn('updated_at');
        }
    }
}
