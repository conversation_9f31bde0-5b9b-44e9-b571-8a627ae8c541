<?php

namespace App\Actions\Search;

use App\Application\Action\AbstractAction;
use App\Application\Exception\MissingParameterException;
use App\Domain\Search\UseCase\FreeFormSearch\FreeFormSearchRequest;
use App\Domain\Search\UseCase\FreeFormSearch\FreeFormSearchUseCase;
use Psr\Http\Message\ResponseInterface as Response;

class SearchAction extends AbstractAction
{
    public function __construct(
        private readonly FreeFormSearchUseCase $useCase
    ) {
    }

    protected function action(): Response
    {
        $params = $this->getParams();

        if (empty($params['q'])) {
            throw new MissingParameterException('q');
        }

        $request = new FreeFormSearchRequest();
        $request->query = $params['q'];
        $request->format = $params['format'] ?? 'jsonv2';
        $request->limit = isset($params['limit']) ? (int) $params['limit'] : 10;
        $request->addressDetails = isset($params['addressdetails']) ? (bool) $params['addressdetails'] : true;
        $request->extraTags = isset($params['extratags']) ? (bool) $params['extratags'] : false;
        $request->nameDetails = isset($params['namedetails']) ? (bool) $params['namedetails'] : false;

        $response = $this->useCase->search($request);

        return $this->respondWithData($response->getResults());
    }
}
