<?php

namespace App\Actions\ApiToken;

use App\Application\Action\AbstractAction;
use App\Domain\ApiToken\ApiTokenRepository;
use App\Domain\ApiToken\UseCase\DeleteToken\DeleteTokenRequest;
use App\Domain\ApiToken\UseCase\DeleteToken\DeleteTokenUseCase;
use App\Domain\User\User;
use Psr\Http\Message\ResponseInterface as Response;
use Slim\Exception\HttpForbiddenException;

class DeleteTokenAction extends AbstractAction
{
    public function __construct(
        private readonly DeleteTokenUseCase $useCase,
        private readonly ApiTokenRepository $tokenRepository
    ) {
    }

    protected function action(): Response
    {
        $userId = (int) $this->resolveArg('id');
        $tokenId = (int) $this->resolveArg('tokenId');
        $currentUser = $this->getCurrentUser();

        // Get the token to check ownership
        $token = $this->tokenRepository->findTokenOfId($tokenId);

        // Authorization check: users can only delete their own tokens, admins can delete any
        if (!$currentUser->isAdmin() && ($currentUser->getId() !== $userId || $token->getUserId() !== $userId)) {
            throw new HttpForbiddenException($this->request, 'You can only delete your own tokens');
        }

        $request = new DeleteTokenRequest();
        $request->id = $tokenId;

        $response = $this->useCase->deleteToken($request);

        return $this->respondWithData(['success' => $response->isSuccess()], 204);
    }

    private function getCurrentUser(): User
    {
        $user = $this->request->getAttribute('user');
        if (!$user instanceof User) {
            throw new \RuntimeException('User not found in request attributes');
        }
        return $user;
    }
}
