<?php

namespace App\Actions\ApiToken;

use App\Application\Action\AbstractAction;
use App\Application\Exception\MissingParameterException;
use App\Domain\ApiToken\UseCase\CreateToken\CreateTokenRequest;
use App\Domain\ApiToken\UseCase\CreateToken\CreateTokenUseCase;
use App\Domain\User\User;
use Psr\Http\Message\ResponseInterface as Response;
use Slim\Exception\HttpForbiddenException;

class CreateTokenAction extends AbstractAction
{
    public function __construct(
        private readonly CreateTokenUseCase $useCase
    ) {
    }

    protected function action(): Response
    {
        $userId = (int) $this->resolveArg('id');
        $currentUser = $this->getCurrentUser();

        // Authorization check: users can only create tokens for themselves, admins can create for any user
        if (!$currentUser->isAdmin() && $currentUser->getId() !== $userId) {
            throw new HttpForbiddenException($this->request, 'You can only create tokens for yourself');
        }

        // Try to get data from body (form data) or JSON
        $body = $this->getBody();
        if (empty($body)) {
            // Try to parse JSON from raw input
            $rawInput = $this->request->getBody()->getContents();
            if (!empty($rawInput)) {
                $jsonData = json_decode($rawInput, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $body = $jsonData;
                }
            }
        }

        if (empty($body['name'])) {
            throw new MissingParameterException('name');
        }

        $request = new CreateTokenRequest();
        $request->name = $body['name'];
        $request->userId = $userId;
        
        // Optional expiration date
        if (!empty($body['expires_at'])) {
            $request->expiresAt = new \DateTime($body['expires_at']);
        }

        $response = $this->useCase->createToken($request);

        return $this->respondWithData($response->getToken(), 201);
    }

    private function getCurrentUser(): User
    {
        $user = $this->request->getAttribute('user');
        if (!$user instanceof User) {
            throw new \RuntimeException('User not found in request attributes');
        }
        return $user;
    }
}
