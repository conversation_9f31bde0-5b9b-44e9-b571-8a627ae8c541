<?php

namespace App\Actions\ApiToken;

use App\Application\Action\AbstractAction;
use App\Domain\ApiToken\UseCase\ListUserTokens\ListUserTokensRequest;
use App\Domain\ApiToken\UseCase\ListUserTokens\ListUserTokensUseCase;
use App\Domain\User\User;
use Psr\Http\Message\ResponseInterface as Response;
use Slim\Exception\HttpForbiddenException;

class ListUserTokensAction extends AbstractAction
{
    public function __construct(
        private readonly ListUserTokensUseCase $useCase
    ) {
    }

    protected function action(): Response
    {
        $userId = (int) $this->resolveArg('id');
        $currentUser = $this->getCurrentUser();

        // Authorization check: users can only see their own tokens, admins can see any
        if (!$currentUser->isAdmin() && $currentUser->getId() !== $userId) {
            throw new HttpForbiddenException($this->request, 'You can only access your own tokens');
        }

        $request = new ListUserTokensRequest();
        $request->userId = $userId;

        $response = $this->useCase->listTokens($request);

        return $this->respondWithData($response->getTokens());
    }

    private function getCurrentUser(): User
    {
        $user = $this->request->getAttribute('user');
        if (!$user instanceof User) {
            throw new \RuntimeException('User not found in request attributes');
        }
        return $user;
    }
}
