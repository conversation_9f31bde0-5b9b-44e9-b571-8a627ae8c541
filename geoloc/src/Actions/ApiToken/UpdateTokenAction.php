<?php

namespace App\Actions\ApiToken;

use App\Application\Action\AbstractAction;
use App\Application\Exception\MissingParameterException;
use App\Domain\ApiToken\ApiTokenRepository;
use App\Domain\ApiToken\UseCase\UpdateToken\UpdateTokenRequest;
use App\Domain\ApiToken\UseCase\UpdateToken\UpdateTokenUseCase;
use App\Domain\User\User;
use Psr\Http\Message\ResponseInterface as Response;
use Slim\Exception\HttpForbiddenException;

class UpdateTokenAction extends AbstractAction
{
    public function __construct(
        private readonly UpdateTokenUseCase $useCase,
        private readonly ApiTokenRepository $tokenRepository
    ) {
    }

    protected function action(): Response
    {
        $userId = (int) $this->resolveArg('id');
        $tokenId = (int) $this->resolveArg('tokenId');
        $currentUser = $this->getCurrentUser();

        // Get the token to check ownership
        $token = $this->tokenRepository->findTokenOfId($tokenId);

        // Authorization check: users can only update their own tokens, admins can update any
        if (!$currentUser->isAdmin() && ($currentUser->getId() !== $userId || $token->getUserId() !== $userId)) {
            throw new HttpForbiddenException($this->request, 'You can only update your own tokens');
        }

        // Try to get data from body (form data) or JSON
        $body = $this->getBody();
        if (empty($body)) {
            // Try to parse JSON from raw input
            $rawInput = $this->request->getBody()->getContents();
            if (!empty($rawInput)) {
                $jsonData = json_decode($rawInput, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $body = $jsonData;
                }
            }
        }

        if (empty($body['name'])) {
            throw new MissingParameterException('name');
        }

        $request = new UpdateTokenRequest();
        $request->id = $tokenId;
        $request->name = $body['name'];
        $request->isActive = isset($body['is_active']) ? (bool) $body['is_active'] : true;

        $response = $this->useCase->updateToken($request);

        return $this->respondWithData($response->getToken());
    }

    private function getCurrentUser(): User
    {
        $user = $this->request->getAttribute('user');
        if (!$user instanceof User) {
            throw new \RuntimeException('User not found in request attributes');
        }
        return $user;
    }
}
