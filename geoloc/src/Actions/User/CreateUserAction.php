<?php

namespace App\Actions\User;

use App\Application\Action\AbstractAction;
use App\Application\Exception\MissingParameterException;
use App\Domain\User\UseCase\CreateUser\CreateUserRequest;
use App\Domain\User\UseCase\CreateUser\CreateUserUseCase;
use Psr\Http\Message\ResponseInterface as Response;

class CreateUserAction extends AbstractAction
{
    public function __construct(
        private readonly CreateUserUseCase $useCase
    ) {
    }

    protected function action(): Response
    {
        // Try to get data from body (form data) or JSON
        $body = $this->getBody();
        if (empty($body)) {
            // Try to parse JSON from raw input
            $rawInput = $this->request->getBody()->getContents();
            if (!empty($rawInput)) {
                $jsonData = json_decode($rawInput, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $body = $jsonData;
                }
            }
        }

        if (empty($body['username'])) {
            throw new MissingParameterException('username');
        }

        if (empty($body['password'])) {
            throw new MissingParameterException('password');
        }

        $request = new CreateUserRequest();
        $request->username = $body['username'];
        $request->password = $body['password'];

        $response = $this->useCase->createUser($request);

        return $this->respondWithData($response->getUser(), 201);
    }
}
