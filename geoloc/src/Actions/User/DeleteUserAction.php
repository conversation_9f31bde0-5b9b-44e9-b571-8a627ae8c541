<?php

namespace App\Actions\User;

use App\Application\Action\AbstractAction;
use App\Domain\User\UseCase\DeleteUser\DeleteUserRequest;
use App\Domain\User\UseCase\DeleteUser\DeleteUserUseCase;
use Psr\Http\Message\ResponseInterface as Response;

class DeleteUserAction extends AbstractAction
{
    public function __construct(
        private readonly DeleteUserUseCase $useCase
    ) {
    }

    protected function action(): Response
    {
        $userId = (int) $this->resolveArg('id');

        $request = new DeleteUserRequest();
        $request->id = $userId;

        $response = $this->useCase->deleteUser($request);

        return $this->respondWithData(['success' => $response->isSuccess()], 204);
    }
}
