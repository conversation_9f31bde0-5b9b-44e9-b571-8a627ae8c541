<?php

namespace App\Actions\User;

use App\Application\Action\AbstractAction;
use App\Application\Exception\MissingParameterException;
use App\Domain\User\UseCase\UpdateUser\UpdateUserRequest;
use App\Domain\User\UseCase\UpdateUser\UpdateUserUseCase;
use Psr\Http\Message\ResponseInterface as Response;

class UpdateUserAction extends AbstractAction
{
    public function __construct(
        private readonly UpdateUserUseCase $useCase
    ) {
    }

    protected function action(): Response
    {
        $userId = (int) $this->resolveArg('id');

        // Try to get data from body (form data) or JSON
        $body = $this->getBody();
        if (empty($body)) {
            // Try to parse JSON from raw input
            $rawInput = $this->request->getBody()->getContents();
            if (!empty($rawInput)) {
                $jsonData = json_decode($rawInput, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $body = $jsonData;
                }
            }
        }

        if (empty($body['username'])) {
            throw new MissingParameterException('username');
        }

        $request = new UpdateUserRequest();
        $request->id = $userId;
        $request->username = $body['username'];

        $response = $this->useCase->updateUser($request);

        return $this->respondWithData($response->getUser());
    }
}
