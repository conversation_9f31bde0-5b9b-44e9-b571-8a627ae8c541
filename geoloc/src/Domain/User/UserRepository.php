<?php

declare(strict_types=1);

namespace App\Domain\User;

use App\Domain\User\Exception\UserNotFoundException;

interface UserRepository
{
    /**
     * @return User[]
     */
    public function findAll(): array;

    /**
     * @throws UserNotFoundException
     */
    public function findUserOfId(int $id): User;

    /**
     * @throws UserNotFoundException
     */
    public function findUserByUsername(string $username): User;

    public function getUserPassword(User $user): string;

    /**
     * @throws UserNotFoundException
     */
    public function findUserByApiKey(string $apiKey): User;

    public function createUser(string $username, string $password): User;

    /**
     * @throws UserNotFoundException
     */
    public function updateUser(int $id, string $username): User;

    /**
     * @throws UserNotFoundException
     */
    public function deleteUser(int $id): void;

    /**
     * @throws UserNotFoundException
     */
    public function findUserByApiToken(string $token): User;
}
