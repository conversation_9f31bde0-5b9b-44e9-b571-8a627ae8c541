<?php

namespace App\Domain\User\UseCase\DeleteUser;

use App\Domain\User\Exception\UserNotFoundException;
use App\Domain\User\UserRepository;

class DeleteUserUseCase
{
    public function __construct(
        private readonly UserRepository $userRepository
    ) {
    }

    /**
     * @throws UserNotFoundException
     */
    public function deleteUser(DeleteUserRequest $request): DeleteUserResponse
    {
        // Verify user exists before deletion
        $this->userRepository->findUserOfId($request->id);

        $this->userRepository->deleteUser($request->id);

        return new DeleteUserResponse(true);
    }
}
