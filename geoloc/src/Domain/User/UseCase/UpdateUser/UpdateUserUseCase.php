<?php

namespace App\Domain\User\UseCase\UpdateUser;

use App\Domain\User\Exception\UserAlreadyExistsException;
use App\Domain\User\Exception\UserNotFoundException;
use App\Domain\User\UserRepository;

class UpdateUserUseCase
{
    public function __construct(
        private readonly UserRepository $userRepository
    ) {
    }

    /**
     * @throws UserNotFoundException
     * @throws UserAlreadyExistsException
     */
    public function updateUser(UpdateUserRequest $request): UpdateUserResponse
    {
        $this->validateRequest($request);

        $user = $this->userRepository->updateUser($request->id, $request->username);

        return new UpdateUserResponse($user);
    }

    private function validateRequest(UpdateUserRequest $request): void
    {
        if (empty($request->username)) {
            throw new \DomainException('Username is required to update a User');
        }

        // Check if user exists
        $this->userRepository->findUserOfId($request->id);

        // Check if username is already taken by another user
        try {
            $existingUser = $this->userRepository->findUserByUsername($request->username);
            if ($existingUser->getId() !== $request->id) {
                throw new UserAlreadyExistsException($request->username);
            }
        } catch (UserNotFoundException $exception) {
            // Username is available, which is good
        }
    }
}
