<?php

declare(strict_types=1);

namespace App\Domain\Search;

class Search implements \JsonSerializable
{
    public function __construct(
        private readonly string $placeId,
        private readonly string $licence,
        private readonly string $osmType,
        private readonly string $osmId,
        private readonly string $lat,
        private readonly string $lon,
        private readonly string $displayName,
        private readonly string $class,
        private readonly string $type,
        private readonly float $importance,
        private readonly ?string $icon = null,
        private readonly array $boundingBox = [],
        private readonly array $address = []
    ) {
    }

    public function getPlaceId(): string
    {
        return $this->placeId;
    }

    public function getLicence(): string
    {
        return $this->licence;
    }

    public function getOsmType(): string
    {
        return $this->osmType;
    }

    public function getOsmId(): string
    {
        return $this->osmId;
    }

    public function getLat(): string
    {
        return $this->lat;
    }

    public function getLon(): string
    {
        return $this->lon;
    }

    public function getDisplayName(): string
    {
        return $this->displayName;
    }

    public function getClass(): string
    {
        return $this->class;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getImportance(): float
    {
        return $this->importance;
    }

    public function getIcon(): ?string
    {
        return $this->icon;
    }

    public function getBoundingBox(): array
    {
        return $this->boundingBox;
    }

    public function getAddress(): array
    {
        return $this->address;
    }

    #[\ReturnTypeWillChange]
    public function jsonSerialize(): array
    {
        return [
            'place_id' => $this->placeId,
            'licence' => $this->licence,
            'osm_type' => $this->osmType,
            'osm_id' => $this->osmId,
            'lat' => $this->lat,
            'lon' => $this->lon,
            'display_name' => $this->displayName,
            'class' => $this->class,
            'type' => $this->type,
            'importance' => $this->importance,
            'icon' => $this->icon,
            'boundingbox' => $this->boundingBox,
            'address' => $this->address,
        ];
    }
}