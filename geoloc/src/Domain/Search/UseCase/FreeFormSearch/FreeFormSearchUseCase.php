<?php

namespace App\Domain\Search\UseCase\FreeFormSearch;

use App\Infrastructure\Search\NominatimSearchService;

class FreeFormSearchUseCase
{
    public function __construct(
        private readonly NominatimSearchService $searchService
    ) {
    }

    public function search(FreeFormSearchRequest $request): FreeFormSearchResponse
    {
        $results = $this->searchService->freeFormSearch(
            query: $request->query,
            format: $request->format,
            limit: $request->limit,
            addressDetails: $request->addressDetails,
            extraTags: $request->extraTags,
            nameDetails: $request->nameDetails
        );

        return new FreeFormSearchResponse($results);
    }
}
