<?php

namespace App\Domain\ApiToken;

use App\Domain\ApiToken\Exception\ApiTokenNotFoundException;

interface ApiTokenRepository
{
    /**
     * @return ApiToken[]
     */
    public function findAll(): array;

    /**
     * @return ApiToken[]
     */
    public function findByUserId(int $userId): array;

    /**
     * @throws ApiTokenNotFoundException
     */
    public function findTokenOfId(int $id): ApiToken;

    /**
     * @throws ApiTokenNotFoundException
     */
    public function findByToken(string $token): ApiToken;

    /**
     * @throws ApiTokenNotFoundException
     */
    public function findValidToken(string $token): ApiToken;

    public function createToken(string $name, int $userId, ?\DateTime $expiresAt = null): ApiToken;

    /**
     * @throws ApiTokenNotFoundException
     */
    public function updateToken(int $id, string $name, bool $isActive): ApiToken;

    /**
     * @throws ApiTokenNotFoundException
     */
    public function deleteToken(int $id): void;
}
