<?php

namespace App\Domain\ApiToken\UseCase\CreateToken;

use App\Domain\ApiToken\ApiTokenRepository;
use App\Domain\User\Exception\UserNotFoundException;
use App\Domain\User\UserRepository;

class CreateTokenUseCase
{
    public function __construct(
        private readonly ApiTokenRepository $tokenRepository,
        private readonly UserRepository $userRepository
    ) {
    }

    /**
     * @throws UserNotFoundException
     */
    public function createToken(CreateTokenRequest $request): CreateTokenResponse
    {
        $this->validateRequest($request);

        // Verify user exists
        $this->userRepository->findUserOfId($request->userId);

        $token = $this->tokenRepository->createToken(
            $request->name,
            $request->userId,
            $request->expiresAt
        );

        return new CreateTokenResponse($token);
    }

    private function validateRequest(CreateTokenRequest $request): void
    {
        if (empty($request->name)) {
            throw new \DomainException('Token name is required');
        }

        if ($request->userId <= 0) {
            throw new \DomainException('Valid user ID is required');
        }
    }
}
