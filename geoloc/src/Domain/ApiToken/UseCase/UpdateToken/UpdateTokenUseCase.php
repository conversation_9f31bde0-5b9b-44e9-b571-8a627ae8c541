<?php

namespace App\Domain\ApiToken\UseCase\UpdateToken;

use App\Domain\ApiToken\ApiTokenRepository;
use App\Domain\ApiToken\Exception\ApiTokenNotFoundException;

class UpdateTokenUseCase
{
    public function __construct(
        private readonly ApiTokenRepository $tokenRepository
    ) {
    }

    /**
     * @throws ApiTokenNotFoundException
     */
    public function updateToken(UpdateTokenRequest $request): UpdateTokenResponse
    {
        $this->validateRequest($request);

        $token = $this->tokenRepository->updateToken(
            $request->id,
            $request->name,
            $request->isActive
        );

        return new UpdateTokenResponse($token);
    }

    private function validateRequest(UpdateTokenRequest $request): void
    {
        if (empty($request->name)) {
            throw new \DomainException('Token name is required');
        }

        if ($request->id <= 0) {
            throw new \DomainException('Valid token ID is required');
        }
    }
}
