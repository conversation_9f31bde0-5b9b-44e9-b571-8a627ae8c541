<?php

namespace App\Domain\ApiToken\UseCase\DeleteToken;

use App\Domain\ApiToken\ApiTokenRepository;
use App\Domain\ApiToken\Exception\ApiTokenNotFoundException;

class DeleteTokenUseCase
{
    public function __construct(
        private readonly ApiTokenRepository $tokenRepository
    ) {
    }

    /**
     * @throws ApiTokenNotFoundException
     */
    public function deleteToken(DeleteTokenRequest $request): DeleteTokenResponse
    {
        // Verify token exists before deletion
        $this->tokenRepository->findTokenOfId($request->id);

        $this->tokenRepository->deleteToken($request->id);

        return new DeleteTokenResponse(true);
    }
}
