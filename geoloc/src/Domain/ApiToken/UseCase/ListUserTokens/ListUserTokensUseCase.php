<?php

namespace App\Domain\ApiToken\UseCase\ListUserTokens;

use App\Domain\ApiToken\ApiTokenRepository;
use App\Domain\User\Exception\UserNotFoundException;
use App\Domain\User\UserRepository;

class ListUserTokensUseCase
{
    public function __construct(
        private readonly ApiTokenRepository $tokenRepository,
        private readonly UserRepository $userRepository
    ) {
    }

    /**
     * @throws UserNotFoundException
     */
    public function listTokens(ListUserTokensRequest $request): ListUserTokensResponse
    {
        // Verify user exists
        $this->userRepository->findUserOfId($request->userId);

        $tokens = $this->tokenRepository->findByUserId($request->userId);

        return new ListUserTokensResponse($tokens);
    }
}
