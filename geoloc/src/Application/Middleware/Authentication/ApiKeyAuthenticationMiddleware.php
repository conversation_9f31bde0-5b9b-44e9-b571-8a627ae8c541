<?php

namespace App\Application\Middleware\Authentication;

use App\Domain\ApiToken\Exception\ApiTokenNotFoundException;
use App\Domain\User\Exception\UserNotFoundException;
use App\Domain\User\UserRepository;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use Slim\Exception\HttpForbiddenException;
use Slim\Exception\HttpUnauthorizedException;

class ApiKeyAuthenticationMiddleware implements MiddlewareInterface
{
    public function __construct(
        private readonly UserRepository $userRepository,
    ) {
    }

    #[\Override]
    public function process(
        Request $request,
        RequestHandlerInterface $handler
    ): Response {
        $token = $this->parseAuthorizationHeader($request);

        try {
            $user = $this->userRepository->findUserByApiToken($token);
        } catch (UserNotFoundException $exception) {
            throw new HttpUnauthorizedException($request, 'Invalid or expired API token');
        }

        // Add user to request attributes for use in actions
        $request = $request->withAttribute('user', $user);

        return $handler->handle($request);
    }

    private function parseAuthorizationHeader(Request $request): string
    {
        $authorizationHeader = $request->getHeaderLine('Authorization');

        if (empty($authorizationHeader)) {
            throw new HttpUnauthorizedException($request, 'Authorization header not provided');
        }

        if (!str_starts_with($authorizationHeader, 'Bearer')) {
            throw new HttpUnauthorizedException($request, 'Authorization method not supported');
        }

        // Get string (api key) after 'Bearer '
        $apiKey = substr($authorizationHeader, 7);
        if (!$apiKey) {
            throw new HttpUnauthorizedException($request, 'Failed to read Authorization header');
        }

        return $apiKey;
    }
}
