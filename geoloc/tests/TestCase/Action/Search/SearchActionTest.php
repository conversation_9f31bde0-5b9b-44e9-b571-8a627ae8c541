<?php

namespace App\Test\TestCase\Action\Search;

use App\Test\Traits\AppTestTrait;
use Fig\Http\Message\StatusCodeInterface;
use PHPUnit\Framework\TestCase;

/**
 * Test.
 *
 * @coversDefaultClass \App\Actions\Search\SearchAction
 *
 * @internal
 */
class SearchActionTest extends TestCase
{
    use AppTestTrait;

    public function testSearchWithValidQuery(): void
    {
        $request = $this->createRequest('GET', '/search?q=Paris&format=jsonv2');
        $response = $this->app->handle($request);

        $this->assertSame(StatusCodeInterface::STATUS_OK, $response->getStatusCode());
        $this->assertJsonContentType($response);
        
        $data = $this->getJsonData($response);
        $this->assertIsArray($data);
    }

    public function testSearchWithMissingQuery(): void
    {
        $request = $this->createRequest('GET', '/search');
        $response = $this->app->handle($request);

        $this->assertSame(StatusCodeInterface::STATUS_BAD_REQUEST, $response->getStatusCode());
    }

    public function testSearchWithCustomFormat(): void
    {
        $request = $this->createRequest('GET', '/search?q=London&format=json');
        $response = $this->app->handle($request);

        $this->assertSame(StatusCodeInterface::STATUS_OK, $response->getStatusCode());
        $this->assertJsonContentType($response);
    }

    public function testSearchWithLimit(): void
    {
        $request = $this->createRequest('GET', '/search?q=Berlin&limit=5');
        $response = $this->app->handle($request);

        $this->assertSame(StatusCodeInterface::STATUS_OK, $response->getStatusCode());
        $this->assertJsonContentType($response);
    }
}
