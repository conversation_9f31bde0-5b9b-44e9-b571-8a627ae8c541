[Mon Jun 30 12:54:19.078656 2025] [log_config:error] [pid 1:tid 1] (13)Permission denied: AH00649: could not open transfer log file /var/log/apache2/other_vhosts_access.log.
AH00015: Unable to open logs
[Mon Jun 30 12:55:18.294226 2025] [log_config:error] [pid 1:tid 1] (13)Permission denied: AH00649: could not open transfer log file /var/log/apache2/other_vhosts_access.log.
AH00015: Unable to open logs
AH00558: apache2: Could not reliably determine the server's fully qualified domain name, using **********. Set the 'ServerName' directive globally to suppress this message
[Mon Jun 30 13:02:56.637628 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00163: Apache/2.4.62 (Debian) PHP/8.3.22 configured -- resuming normal operations
[Mon Jun 30 13:02:56.637659 2025] [core:notice] [pid 1:tid 1] AH00094: Command line: 'apache2 -D FOREGROUND'
[Mon Jun 30 13:07:16.978503 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00170: caught SIGWINCH, shutting down gracefully
AH00558: apache2: Could not reliably determine the server's fully qualified domain name, using **********. Set the 'ServerName' directive globally to suppress this message
[Mon Jun 30 13:07:21.479771 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00163: Apache/2.4.62 (Debian) PHP/8.3.22 configured -- resuming normal operations
[Mon Jun 30 13:07:21.479798 2025] [core:notice] [pid 1:tid 1] AH00094: Command line: 'apache2 -D FOREGROUND'
[Mon Jun 30 13:08:11.528462 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00170: caught SIGWINCH, shutting down gracefully
AH00558: apache2: Could not reliably determine the server's fully qualified domain name, using **********. Set the 'ServerName' directive globally to suppress this message
[Mon Jun 30 13:08:14.452755 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00163: Apache/2.4.62 (Debian) PHP/8.3.22 configured -- resuming normal operations
[Mon Jun 30 13:08:14.452791 2025] [core:notice] [pid 1:tid 1] AH00094: Command line: 'apache2 -D FOREGROUND'
[Mon Jun 30 13:08:51.432616 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00170: caught SIGWINCH, shutting down gracefully
AH00558: apache2: Could not reliably determine the server's fully qualified domain name, using 172.19.0.3. Set the 'ServerName' directive globally to suppress this message
[Mon Jun 30 13:09:03.174946 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00163: Apache/2.4.62 (Debian) PHP/8.3.22 configured -- resuming normal operations
[Mon Jun 30 13:09:03.174978 2025] [core:notice] [pid 1:tid 1] AH00094: Command line: 'apache2 -D FOREGROUND'
[Mon Jun 30 13:09:32.828962 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00170: caught SIGWINCH, shutting down gracefully
AH00558: apache2: Could not reliably determine the server's fully qualified domain name, using 172.19.0.3. Set the 'ServerName' directive globally to suppress this message
[Mon Jun 30 13:09:35.352970 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00163: Apache/2.4.62 (Debian) PHP/8.3.22 configured -- resuming normal operations
[Mon Jun 30 13:09:35.353002 2025] [core:notice] [pid 1:tid 1] AH00094: Command line: 'apache2 -D FOREGROUND'
[Mon Jun 30 13:09:57.617492 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00170: caught SIGWINCH, shutting down gracefully
AH00558: apache2: Could not reliably determine the server's fully qualified domain name, using 172.19.0.3. Set the 'ServerName' directive globally to suppress this message
[Mon Jun 30 13:10:00.148569 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00163: Apache/2.4.62 (Debian) PHP/8.3.22 configured -- resuming normal operations
[Mon Jun 30 13:10:00.148612 2025] [core:notice] [pid 1:tid 1] AH00094: Command line: 'apache2 -D FOREGROUND'
[Mon Jun 30 13:13:23.515922 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00170: caught SIGWINCH, shutting down gracefully
AH00558: apache2: Could not reliably determine the server's fully qualified domain name, using 172.19.0.4. Set the 'ServerName' directive globally to suppress this message
[Mon Jun 30 13:13:26.884187 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00163: Apache/2.4.62 (Debian) PHP/8.3.22 configured -- resuming normal operations
[Mon Jun 30 13:13:26.884219 2025] [core:notice] [pid 1:tid 1] AH00094: Command line: 'apache2 -D FOREGROUND'
[Mon Jun 30 14:26:41.872953 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00170: caught SIGWINCH, shutting down gracefully
AH00558: apache2: Could not reliably determine the server's fully qualified domain name, using 172.19.0.4. Set the 'ServerName' directive globally to suppress this message
[Mon Jun 30 14:26:44.419658 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00163: Apache/2.4.62 (Debian) PHP/8.3.22 configured -- resuming normal operations
[Mon Jun 30 14:26:44.419693 2025] [core:notice] [pid 1:tid 1] AH00094: Command line: 'apache2 -D FOREGROUND'
[Mon Jun 30 14:29:56.864921 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00170: caught SIGWINCH, shutting down gracefully
AH00558: apache2: Could not reliably determine the server's fully qualified domain name, using 172.19.0.4. Set the 'ServerName' directive globally to suppress this message
[Mon Jun 30 14:29:59.329581 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00163: Apache/2.4.62 (Debian) PHP/8.3.22 configured -- resuming normal operations
[Mon Jun 30 14:29:59.329617 2025] [core:notice] [pid 1:tid 1] AH00094: Command line: 'apache2 -D FOREGROUND'
[Mon Jun 30 15:06:33.177418 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00170: caught SIGWINCH, shutting down gracefully
AH00558: apache2: Could not reliably determine the server's fully qualified domain name, using 172.19.0.4. Set the 'ServerName' directive globally to suppress this message
[Mon Jun 30 15:06:35.793156 2025] [mpm_prefork:notice] [pid 1:tid 1] AH00163: Apache/2.4.62 (Debian) PHP/8.3.22 configured -- resuming normal operations
[Mon Jun 30 15:06:35.793195 2025] [core:notice] [pid 1:tid 1] AH00094: Command line: 'apache2 -D FOREGROUND'
