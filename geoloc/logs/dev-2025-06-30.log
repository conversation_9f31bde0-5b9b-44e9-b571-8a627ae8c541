{"message":"GET / - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:00:19.390","dateEnd":"2025-06-30 12:00:19.392"},"request":{"method":"GET","path":"/","params":[],"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"Hello world!"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T12:00:19.392620+02:00","extra":{}}
{"message":"GET / - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:00:26.113","dateEnd":"2025-06-30 12:00:26.114"},"request":{"method":"GET","path":"/","params":[],"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"Hello world!"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T12:00:26.114515+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:00:57.208122+02:00","extra":{}}
{"message":"GET /test - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:00:57.207","dateEnd":"2025-06-30 12:00:57.208"},"request":{"method":"GET","path":"/test","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:00:57.208602+02:00","extra":{}}
{"message":"GET / - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:01:56.193","dateEnd":"2025-06-30 12:01:56.194"},"request":{"method":"GET","path":"/","params":[],"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"Hello world!"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T12:01:56.194079+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:05:28.771975+02:00","extra":{}}
{"message":"GET /test - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:05:28.771","dateEnd":"2025-06-30 12:05:28.772"},"request":{"method":"GET","path":"/test","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:05:28.772251+02:00","extra":{}}
{"message":"GET / - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:09:12.422","dateEnd":"2025-06-30 12:09:12.422"},"request":{"method":"GET","path":"/","params":[],"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"Hello world!"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T12:09:12.423036+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:14:18.733716+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:14:18.732","dateEnd":"2025-06-30 12:14:18.734"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:14:18.734147+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:14:26.525442+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:14:26.525","dateEnd":"2025-06-30 12:14:26.525"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:14:26.525699+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:15:35.435330+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:15:35.434","dateEnd":"2025-06-30 12:15:35.435"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:15:35.435731+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:15:44.930350+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:15:44.927","dateEnd":"2025-06-30 12:15:44.931"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:15:44.931239+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:15:45.522729+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:15:45.522","dateEnd":"2025-06-30 12:15:45.522"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:15:45.522943+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:15:46.414709+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:15:46.414","dateEnd":"2025-06-30 12:15:46.414"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:15:46.415017+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:15:47.331565+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:15:47.331","dateEnd":"2025-06-30 12:15:47.331"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:15:47.331879+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:15:48.255538+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:15:48.255","dateEnd":"2025-06-30 12:15:48.255"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:15:48.255753+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:15:49.755776+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:15:49.755","dateEnd":"2025-06-30 12:15:49.756"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:15:49.756034+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:15:50.825203+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:15:50.824","dateEnd":"2025-06-30 12:15:50.825"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:15:50.825445+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:16:33.058230+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:16:33.057","dateEnd":"2025-06-30 12:16:33.058"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:16:33.058545+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:16:58.852388+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:16:58.851","dateEnd":"2025-06-30 12:16:58.852"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:16:58.852687+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:17:57.889721+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:17:57.889","dateEnd":"2025-06-30 12:17:57.889"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:17:57.890012+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:18:10.996166+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:18:10.995","dateEnd":"2025-06-30 12:18:10.996"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:18:10.996439+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:19:38.945157+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:19:38.944","dateEnd":"2025-06-30 12:19:38.945"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:19:38.945515+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:19:46.847202+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:19:46.846","dateEnd":"2025-06-30 12:19:46.847"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:19:46.847451+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T12:22:39.929772+02:00","extra":{}}
{"message":"GET /search - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:22:39.929","dateEnd":"2025-06-30 12:22:39.930"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T12:22:39.930060+02:00","extra":{}}
{"message":"GET /search - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 12:24:38.319","dateEnd":"2025-06-30 12:24:38.319"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"searched!"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T12:24:38.319702+02:00","extra":{}}
{"message":"Method not allowed. Must be one of: OPTIONS","context":{"message":"Method not allowed. Must be one of: OPTIONS","file":"/var/www/vendor/slim/slim/Slim/Middleware/RoutingMiddleware.php","line":79,"trace":"#0 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(62): Slim\\Middleware\\RoutingMiddleware->performRouting(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#3 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#5 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#11 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T14:43:05.211279+02:00","extra":{}}
{"message":"GET /search/ - 405 Method Not Allowed","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 14:43:05.210","dateEnd":"2025-06-30 14:43:05.211"},"request":{"method":"GET","path":"/search/","params":{"id":"1"},"body":null},"response":{"statusCode":405,"reasonPhrase":"Method Not Allowed","body":"{\n    \"statusCode\": 405,\n    \"error\": {\n        \"error\": \"NOT_ALLOWED\",\n        \"description\": \"Method not allowed. Must be one of: OPTIONS\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T14:43:05.211556+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"https://nominatim.openstreetmap.org/search?q=test&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T14:43:15.141542+02:00","extra":{}}
{"message":"Nominatim API request successful","context":{"result_count":10},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T14:43:15.561363+02:00","extra":{}}
{"message":"GET /search - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 14:43:15.140","dateEnd":"2025-06-30 14:43:15.562"},"request":{"method":"GET","path":"/search","params":{"q":"test"},"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"[{\"place_id\":196942149,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":948874455,\"lat\":\"34.0218976\",\"lon\":\"50.6951851\",\"category\":\"waterway\",\"type\":\"canal\",\"place_rank\":20,\"importance\":0.13338817196917677,\"addresstype\":\"canal\",\"name\":\"test\",\"display_name\":\"test, \\u062f\\u0647\\u0633\\u062a\\u0627\\u0646 \\u062f\\u0648\\u062f\\u0647\\u06a9, \\u0628\\u062e\\u0634 \\u0645\\u0631\\u06a9\\u0632\\u06cc, \\u0634\\u0647\\u0631\\u0633\\u062a\\u0627\\u0646 \\u062f\\u0644\\u06cc\\u062c\\u0627\\u0646, \\u0627\\u0633\\u062a\\u0627\\u0646 \\u0645\\u0631\\u06a9\\u0632\\u06cc, \\u0627\\u06cc\\u0631\\u0627\\u0646\",\"address\":{\"canal\":\"test\",\"city\":\"\\u062f\\u0647\\u0633\\u062a\\u0627\\u0646 \\u062f\\u0648\\u062f\\u0647\\u06a9\",\"district\":\"\\u0628\\u062e\\u0634 \\u0645\\u0631\\u06a9\\u0632\\u06cc\",\"county\":\"\\u0634\\u0647\\u0631\\u0633\\u062a\\u0627\\u0646 \\u062f\\u0644\\u06cc\\u062c\\u0627\\u0646\",\"state\":\"\\u0627\\u0633\\u062a\\u0627\\u0646 \\u0645\\u0631\\u06a9\\u0632\\u06cc\",\"ISO3166-2-lvl4\":\"IR-00\",\"country\":\"\\u0627\\u06cc\\u0631\\u0627\\u0646\",\"country_code\":\"ir\"},\"boundingbox\":[\"34.0194329\",\"34.0229848\",\"50.6907811\",\"50.7001773\"]},{\"place_id\":195042741,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":948874451,\"lat\":\"33.9792023\",\"lon\":\"50.6831977\",\"category\":\"waterway\",\"type\":\"canal\",\"place_rank\":20,\"importance\":0.13338817196917677,\"addresstype\":\"canal\",\"name\":\"test\",\"display_name\":\"test, \\u062f\\u0647\\u0633\\u062a\\u0627\\u0646 \\u0647\\u0633\\u062a\\u06cc\\u062c\\u0627\\u0646, \\u0628\\u062e\\u0634 \\u0645\\u0631\\u06a9\\u0632\\u06cc, \\u0634\\u0647\\u0631\\u0633\\u062a\\u0627\\u0646 \\u062f\\u0644\\u06cc\\u062c\\u0627\\u0646, \\u0627\\u0633\\u062a\\u0627\\u0646 \\u0645\\u0631\\u06a9\\u0632\\u06cc, \\u0627\\u06cc\\u0631\\u0627\\u0646\",\"address\":{\"canal\":\"test\",\"city\":\"\\u062f\\u0647\\u0633\\u062a\\u0627\\u0646 \\u0647\\u0633\\u062a\\u06cc\\u062c\\u0627\\u0646\",\"district\":\"\\u0628\\u062e\\u0634 \\u0645\\u0631\\u06a9\\u0632\\u06cc\",\"county\":\"\\u0634\\u0647\\u0631\\u0633\\u062a\\u0627\\u0646 \\u062f\\u0644\\u06cc\\u062c\\u0627\\u0646\",\"state\":\"\\u0627\\u0633\\u062a\\u0627\\u0646 \\u0645\\u0631\\u06a9\\u0632\\u06cc\",\"ISO3166-2-lvl4\":\"IR-00\",\"country\":\"\\u0627\\u06cc\\u0631\\u0627\\u0646\",\"country_code\":\"ir\"},\"boundingbox\":[\"33.9776990\",\"33.9808828\",\"50.6807452\",\"50.6857275\"]},{\"place_id\":193924479,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":948874453,\"lat\":\"33.9809254\",\"lon\":\"50.6854749\",\"category\":\"waterway\",\"type\":\"canal\",\"place_rank\":20,\"importance\":0.13338817196917677,\"addresstype\":\"canal\",\"name\":\"test\",\"display_name\":\"test, \\u0634\\u0647\\u0631 \\u062f\\u0644\\u06cc\\u062c\\u0627\\u0646, \\u0628\\u062e\\u0634 \\u0645\\u0631\\u06a9\\u0632\\u06cc, \\u0634\\u0647\\u0631\\u0633\\u062a\\u0627\\u0646 \\u062f\\u0644\\u06cc\\u062c\\u0627\\u0646, \\u0627\\u0633\\u062a\\u0627\\u0646 \\u0645\\u0631\\u06a9\\u0632\\u06cc, \\u0627\\u06cc\\u0631\\u0627\\u0646\",\"address\":{\"canal\":\"test\",\"town\":\"\\u0634\\u0647\\u0631 \\u062f\\u0644\\u06cc\\u062c\\u0627\\u0646\",\"district\":\"\\u0628\\u062e\\u0634 \\u0645\\u0631\\u06a9\\u0632\\u06cc\",\"county\":\"\\u0634\\u0647\\u0631\\u0633\\u062a\\u0627\\u0646 \\u062f\\u0644\\u06cc\\u062c\\u0627\\u0646\",\"state\":\"\\u0627\\u0633\\u062a\\u0627\\u0646 \\u0645\\u0631\\u06a9\\u0632\\u06cc\",\"ISO3166-2-lvl4\":\"IR-00\",\"country\":\"\\u0627\\u06cc\\u0631\\u0627\\u0646\",\"country_code\":\"ir\"},\"boundingbox\":[\"33.9802482\",\"33.9817691\",\"50.6847574\",\"50.6864040\"]},{\"place_id\":196361335,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":946940560,\"lat\":\"33.4656407\",\"lon\":\"51.1562679\",\"category\":\"waterway\",\"type\":\"canal\",\"place_rank\":20,\"importance\":0.1333833348592355,\"addresstype\":\"canal\",\"name\":\"test\",\"display_name\":\"test, \\u062f\\u0647\\u0633\\u062a\\u0627\\u0646 \\u0648\\u0646\\u062f\\u0627\\u062f\\u0647, \\u0628\\u062e\\u0634 \\u0645\\u06cc\\u0645\\u0647, \\u0634\\u0647\\u0631\\u0633\\u062a\\u0627\\u0646 \\u0634\\u0627\\u0647\\u06cc\\u0646 \\u0634\\u0647\\u0631 \\u0648 \\u0645\\u06cc\\u0645\\u0647, \\u0627\\u0633\\u062a\\u0627\\u0646 \\u0627\\u0635\\u0641\\u0647\\u0627\\u0646, 83517-53911, \\u0627\\u06cc\\u0631\\u0627\\u0646\",\"address\":{\"canal\":\"test\",\"city\":\"\\u062f\\u0647\\u0633\\u062a\\u0627\\u0646 \\u0648\\u0646\\u062f\\u0627\\u062f\\u0647\",\"district\":\"\\u0628\\u062e\\u0634 \\u0645\\u06cc\\u0645\\u0647\",\"county\":\"\\u0634\\u0647\\u0631\\u0633\\u062a\\u0627\\u0646 \\u0634\\u0627\\u0647\\u06cc\\u0646 \\u0634\\u0647\\u0631 \\u0648 \\u0645\\u06cc\\u0645\\u0647\",\"state\":\"\\u0627\\u0633\\u062a\\u0627\\u0646 \\u0627\\u0635\\u0641\\u0647\\u0627\\u0646\",\"ISO3166-2-lvl4\":\"IR-10\",\"postcode\":\"83517-53911\",\"country\":\"\\u0627\\u06cc\\u0631\\u0627\\u0646\",\"country_code\":\"ir\"},\"boundingbox\":[\"33.4647218\",\"33.4666188\",\"51.1558780\",\"51.1563218\"]},{\"place_id\":*********,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":1394434356,\"lat\":\"-32.4774928\",\"lon\":\"150.6588892\",\"category\":\"landuse\",\"type\":\"forest\",\"place_rank\":22,\"importance\":0.10671312962539099,\"addresstype\":\"forest\",\"name\":\"test (deletable)\",\"display_name\":\"test (deletable), Muswellbrook Shire Council, New South Wales, Australia\",\"address\":{\"forest\":\"test (deletable)\",\"municipality\":\"Muswellbrook Shire Council\",\"state\":\"New South Wales\",\"ISO3166-2-lvl4\":\"AU-NSW\",\"country\":\"Australia\",\"country_code\":\"au\"},\"boundingbox\":[\"-32.4831120\",\"-32.4718274\",\"150.6532482\",\"150.6645139\"]},{\"place_id\":*********,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"node\",\"osm_id\":***********,\"lat\":\"10.9909276\",\"lon\":\"76.4737788\",\"category\":\"leisure\",\"type\":\"park\",\"place_rank\":24,\"importance\":0.08004564812695503,\"addresstype\":\"park\",\"name\":\"Test\",\"display_name\":\"Test, Nottanmala, Mannarkad, Palakkad, Kerala, 678582, India\",\"address\":{\"park\":\"Test\",\"village\":\"Nottanmala\",\"county\":\"Mannarkad\",\"state_district\":\"Palakkad\",\"state\":\"Kerala\",\"ISO3166-2-lvl4\":\"IN-KL\",\"postcode\":\"678582\",\"country\":\"India\",\"country_code\":\"in\"},\"boundingbox\":[\"10.9908776\",\"10.9909776\",\"76.4737288\",\"76.4738288\"]},{\"place_id\":48219788,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":1095833366,\"lat\":\"38.5882518\",\"lon\":\"15.9374641\",\"category\":\"highway\",\"type\":\"track\",\"place_rank\":26,\"importance\":0.0533880743114366,\"addresstype\":\"road\",\"name\":\"test\",\"display_name\":\"test, Comerconi, Nicotera, Vibo Valentia, Calabria, 89844, Italia\",\"address\":{\"road\":\"test\",\"hamlet\":\"Comerconi\",\"town\":\"Nicotera\",\"county\":\"Vibo Valentia\",\"ISO3166-2-lvl6\":\"IT-VV\",\"state\":\"Calabria\",\"ISO3166-2-lvl4\":\"IT-78\",\"postcode\":\"89844\",\"country\":\"Italia\",\"country_code\":\"it\"},\"boundingbox\":[\"38.5879462\",\"38.5886311\",\"15.9370303\",\"15.9377512\"]},{\"place_id\":47739939,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":396904765,\"lat\":\"38.5911594\",\"lon\":\"15.9359532\",\"category\":\"highway\",\"type\":\"track\",\"place_rank\":26,\"importance\":0.0533880743114366,\"addresstype\":\"road\",\"name\":\"test\",\"display_name\":\"test, Nicotera, Vibo Valentia, Calabria, 89844, Italia\",\"address\":{\"road\":\"test\",\"town\":\"Nicotera\",\"county\":\"Vibo Valentia\",\"ISO3166-2-lvl6\":\"IT-VV\",\"state\":\"Calabria\",\"ISO3166-2-lvl4\":\"IT-78\",\"postcode\":\"89844\",\"country\":\"Italia\",\"country_code\":\"it\"},\"boundingbox\":[\"38.5906263\",\"38.5917077\",\"15.9358362\",\"15.9360053\"]},{\"place_id\":196873456,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":215043552,\"lat\":\"34.6426534\",\"lon\":\"69.0728981\",\"category\":\"highway\",\"type\":\"residential\",\"place_rank\":26,\"importance\":0.05337833447775994,\"addresstype\":\"road\",\"name\":\"test\",\"display_name\":\"test, \\u0648\\u0644\\u0633\\u0648\\u0627\\u0644\\u06cc \\u0634\\u06a9\\u0631\\u062f\\u0631\\u0647, \\u0648\\u0644\\u0627\\u06cc\\u062a \\u0643\\u0627\\u0628\\u0644, 1061, \\u0627\\u0641\\u063a\\u0627\\u0646\\u0633\\u062a\\u0627\\u0646\",\"address\":{\"road\":\"test\",\"county\":\"\\u0648\\u0644\\u0633\\u0648\\u0627\\u0644\\u06cc \\u0634\\u06a9\\u0631\\u062f\\u0631\\u0647\",\"state\":\"\\u0648\\u0644\\u0627\\u06cc\\u062a \\u0643\\u0627\\u0628\\u0644\",\"ISO3166-2-lvl4\":\"AF-KAB\",\"postcode\":\"1061\",\"country\":\"\\u0627\\u0641\\u063a\\u0627\\u0646\\u0633\\u062a\\u0627\\u0646\",\"country_code\":\"af\"},\"boundingbox\":[\"34.6423177\",\"34.6432849\",\"69.0726381\",\"69.0735071\"]},{\"place_id\":396733302,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":1350904578,\"lat\":\"-5.0928470\",\"lon\":\"104.9125486\",\"category\":\"highway\",\"type\":\"residential\",\"place_rank\":26,\"importance\":0.053370834668497714,\"addresstype\":\"road\",\"name\":\"test\",\"display_name\":\"test, Payung Batu, Lampung Tengah, Lampung, Sumatera, Indonesia\",\"address\":{\"road\":\"test\",\"village\":\"Payung Batu\",\"county\":\"Lampung Tengah\",\"state\":\"Lampung\",\"ISO3166-2-lvl4\":\"ID-LA\",\"region\":\"Sumatera\",\"ISO3166-2-lvl3\":\"ID-SM\",\"country\":\"Indonesia\",\"country_code\":\"id\"},\"boundingbox\":[\"-5.0939157\",\"-5.0916841\",\"104.9124067\",\"104.9130653\"]}]"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T14:43:15.562317+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"https://nominatim.openstreetmap.org/search?q=144+rue+d%27odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T14:43:28.308753+02:00","extra":{}}
{"message":"Nominatim API request successful","context":{"result_count":2},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T14:43:28.517203+02:00","extra":{}}
{"message":"GET /search - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 14:43:28.308","dateEnd":"2025-06-30 14:43:28.517"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue d'odin"},"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"[{\"place_id\":77398182,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":999716924,\"lat\":\"43.6157780\",\"lon\":\"3.9149826\",\"category\":\"building\",\"type\":\"yes\",\"place_rank\":30,\"importance\":6.174486915384172e-5,\"addresstype\":\"building\",\"name\":\"Le Mustang\",\"display_name\":\"Le Mustang, 144, Rue d'Odin, Le Mill\\u00e9naire, Mill\\u00e9naire, Port Marianne, Montpellier, H\\u00e9rault, Occitanie, France m\\u00e9tropolitaine, 34000, France\",\"address\":{\"building\":\"Le Mustang\",\"house_number\":\"144\",\"road\":\"Rue d'Odin\",\"neighbourhood\":\"Le Mill\\u00e9naire\",\"suburb\":\"Port Marianne\",\"city\":\"Montpellier\",\"municipality\":\"Montpellier\",\"county\":\"H\\u00e9rault\",\"ISO3166-2-lvl6\":\"FR-34\",\"state\":\"Occitanie\",\"ISO3166-2-lvl4\":\"FR-OCC\",\"region\":\"France m\\u00e9tropolitaine\",\"postcode\":\"34000\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"43.6154811\",\"43.6160648\",\"3.9147473\",\"3.9151402\"]},{\"place_id\":77269695,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"node\",\"osm_id\":4883631518,\"lat\":\"43.6158695\",\"lon\":\"3.9154484\",\"category\":\"place\",\"type\":\"house\",\"place_rank\":30,\"importance\":6.174486915384172e-5,\"addresstype\":\"place\",\"name\":\"\",\"display_name\":\"144, Rue d'Odin, Le Mill\\u00e9naire, Mill\\u00e9naire, Port Marianne, Montpellier, H\\u00e9rault, Occitanie, France m\\u00e9tropolitaine, 34965, France\",\"address\":{\"house_number\":\"144\",\"road\":\"Rue d'Odin\",\"neighbourhood\":\"Le Mill\\u00e9naire\",\"suburb\":\"Port Marianne\",\"city\":\"Montpellier\",\"municipality\":\"Montpellier\",\"county\":\"H\\u00e9rault\",\"ISO3166-2-lvl6\":\"FR-34\",\"state\":\"Occitanie\",\"ISO3166-2-lvl4\":\"FR-OCC\",\"region\":\"France m\\u00e9tropolitaine\",\"postcode\":\"34965\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"43.6158195\",\"43.6159195\",\"3.9153984\",\"3.9154984\"]}]"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T14:43:28.518010+02:00","extra":{}}
{"message":"Uncaught Error: Undefined constant App\\Infrastructure\\Search\\NominatimSearchService::NOMINATIM_BASE_URL in /var/www/src/Infrastructure/Search/NominatimSearchService.php:98\nStack trace:\n#0 /var/www/src/Infrastructure/Search/NominatimSearchService.php(40): App\\Infrastructure\\Search\\NominatimSearchService->buildSearchUrl('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#2 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#3 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#4 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#11 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#12 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#13 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#14 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#16 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#19 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#20 {main}\n  thrown","context":{"type":1,"message":"Uncaught Error: Undefined constant App\\Infrastructure\\Search\\NominatimSearchService::NOMINATIM_BASE_URL in /var/www/src/Infrastructure/Search/NominatimSearchService.php:98\nStack trace:\n#0 /var/www/src/Infrastructure/Search/NominatimSearchService.php(40): App\\Infrastructure\\Search\\NominatimSearchService->buildSearchUrl('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#2 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#3 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#4 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#11 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#12 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#13 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#14 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#16 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#19 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#20 {main}\n  thrown","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":98},"level":500,"level_name":"CRITICAL","channel":"app","datetime":"2025-06-30T14:48:15.933727+02:00","extra":{}}
{"message":"Uncaught Error: Undefined constant App\\Infrastructure\\Search\\NominatimSearchService::NOMINATIM_BASE_URL in /var/www/src/Infrastructure/Search/NominatimSearchService.php:98\nStack trace:\n#0 /var/www/src/Infrastructure/Search/NominatimSearchService.php(40): App\\Infrastructure\\Search\\NominatimSearchService->buildSearchUrl('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#2 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#3 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#4 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#11 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#12 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#13 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#14 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#16 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#19 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#20 {main}\n  thrown","context":{"type":1,"message":"Uncaught Error: Undefined constant App\\Infrastructure\\Search\\NominatimSearchService::NOMINATIM_BASE_URL in /var/www/src/Infrastructure/Search/NominatimSearchService.php:98\nStack trace:\n#0 /var/www/src/Infrastructure/Search/NominatimSearchService.php(40): App\\Infrastructure\\Search\\NominatimSearchService->buildSearchUrl('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#2 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#3 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#4 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#11 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#12 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#13 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#14 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#16 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#19 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#20 {main}\n  thrown","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":98},"level":500,"level_name":"CRITICAL","channel":"app","datetime":"2025-06-30T14:48:56.089279+02:00","extra":{}}
{"message":"Uncaught Error: Undefined constant App\\Infrastructure\\Search\\NominatimSearchService::NOMINATIM_BASE_URL in /var/www/src/Infrastructure/Search/NominatimSearchService.php:98\nStack trace:\n#0 /var/www/src/Infrastructure/Search/NominatimSearchService.php(40): App\\Infrastructure\\Search\\NominatimSearchService->buildSearchUrl('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#2 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#3 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#4 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#11 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#12 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#13 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#14 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#16 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#19 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#20 {main}\n  thrown","context":{"type":1,"message":"Uncaught Error: Undefined constant App\\Infrastructure\\Search\\NominatimSearchService::NOMINATIM_BASE_URL in /var/www/src/Infrastructure/Search/NominatimSearchService.php:98\nStack trace:\n#0 /var/www/src/Infrastructure/Search/NominatimSearchService.php(40): App\\Infrastructure\\Search\\NominatimSearchService->buildSearchUrl('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#2 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#3 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#4 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#11 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#12 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#13 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#14 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#16 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#19 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#20 {main}\n  thrown","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":98},"level":500,"level_name":"CRITICAL","channel":"app","datetime":"2025-06-30T14:48:57.141128+02:00","extra":{}}
{"message":"Uncaught Error: Undefined constant App\\Infrastructure\\Search\\NominatimSearchService::NOMINATIM_BASE_URL in /var/www/src/Infrastructure/Search/NominatimSearchService.php:98\nStack trace:\n#0 /var/www/src/Infrastructure/Search/NominatimSearchService.php(40): App\\Infrastructure\\Search\\NominatimSearchService->buildSearchUrl('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#2 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#3 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#4 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#11 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#12 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#13 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#14 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#16 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#19 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#20 {main}\n  thrown","context":{"type":1,"message":"Uncaught Error: Undefined constant App\\Infrastructure\\Search\\NominatimSearchService::NOMINATIM_BASE_URL in /var/www/src/Infrastructure/Search/NominatimSearchService.php:98\nStack trace:\n#0 /var/www/src/Infrastructure/Search/NominatimSearchService.php(40): App\\Infrastructure\\Search\\NominatimSearchService->buildSearchUrl('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#2 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#3 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#4 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#11 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#12 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#13 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#14 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#16 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#19 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#20 {main}\n  thrown","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":98},"level":500,"level_name":"CRITICAL","channel":"app","datetime":"2025-06-30T14:48:58.408198+02:00","extra":{}}
{"message":"Uncaught Error: Undefined constant App\\Infrastructure\\Search\\NominatimSearchService::NOMINATIM_BASE_URL in /var/www/src/Infrastructure/Search/NominatimSearchService.php:98\nStack trace:\n#0 /var/www/src/Infrastructure/Search/NominatimSearchService.php(40): App\\Infrastructure\\Search\\NominatimSearchService->buildSearchUrl('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#2 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#3 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#4 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#11 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#12 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#13 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#14 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#16 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#19 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#20 {main}\n  thrown","context":{"type":1,"message":"Uncaught Error: Undefined constant App\\Infrastructure\\Search\\NominatimSearchService::NOMINATIM_BASE_URL in /var/www/src/Infrastructure/Search/NominatimSearchService.php:98\nStack trace:\n#0 /var/www/src/Infrastructure/Search/NominatimSearchService.php(40): App\\Infrastructure\\Search\\NominatimSearchService->buildSearchUrl('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#2 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#3 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#4 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#11 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#12 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#13 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#14 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#16 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#19 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#20 {main}\n  thrown","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":98},"level":500,"level_name":"CRITICAL","channel":"app","datetime":"2025-06-30T14:49:01.376940+02:00","extra":{}}
{"message":"Uncaught Error: Undefined constant App\\Infrastructure\\Search\\NominatimSearchService::NOMINATIM_BASE_URL in /var/www/src/Infrastructure/Search/NominatimSearchService.php:98\nStack trace:\n#0 /var/www/src/Infrastructure/Search/NominatimSearchService.php(40): App\\Infrastructure\\Search\\NominatimSearchService->buildSearchUrl('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#2 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#3 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#4 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#11 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#12 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#13 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#14 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#16 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#19 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#20 {main}\n  thrown","context":{"type":1,"message":"Uncaught Error: Undefined constant App\\Infrastructure\\Search\\NominatimSearchService::NOMINATIM_BASE_URL in /var/www/src/Infrastructure/Search/NominatimSearchService.php:98\nStack trace:\n#0 /var/www/src/Infrastructure/Search/NominatimSearchService.php(40): App\\Infrastructure\\Search\\NominatimSearchService->buildSearchUrl('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#2 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#3 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#4 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#11 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#12 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#13 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#14 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#16 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#19 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#20 {main}\n  thrown","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":98},"level":500,"level_name":"CRITICAL","channel":"app","datetime":"2025-06-30T14:49:02.272616+02:00","extra":{}}
{"message":"Uncaught Error: Undefined constant App\\Infrastructure\\Search\\NominatimSearchService::NOMINATIM_BASE_URL in /var/www/src/Infrastructure/Search/NominatimSearchService.php:98\nStack trace:\n#0 /var/www/src/Infrastructure/Search/NominatimSearchService.php(40): App\\Infrastructure\\Search\\NominatimSearchService->buildSearchUrl('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#2 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#3 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#4 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#11 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#12 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#13 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#14 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#16 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#19 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#20 {main}\n  thrown","context":{"type":1,"message":"Uncaught Error: Undefined constant App\\Infrastructure\\Search\\NominatimSearchService::NOMINATIM_BASE_URL in /var/www/src/Infrastructure/Search/NominatimSearchService.php:98\nStack trace:\n#0 /var/www/src/Infrastructure/Search/NominatimSearchService.php(40): App\\Infrastructure\\Search\\NominatimSearchService->buildSearchUrl('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue d'odin', 'jsonv2', 10, true, false, false)\n#2 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#3 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#4 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#11 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#12 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#13 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#14 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#16 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#19 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#20 {main}\n  thrown","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":98},"level":500,"level_name":"CRITICAL","channel":"app","datetime":"2025-06-30T14:49:13.236130+02:00","extra":{}}
{"message":"GET / - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:06:23.138","dateEnd":"2025-06-30 15:06:23.139"},"request":{"method":"GET","path":"/","params":[],"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"Hello world!"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T15:06:23.140035+02:00","extra":{}}
{"message":"GET / - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:06:28.285","dateEnd":"2025-06-30 15:06:28.286"},"request":{"method":"GET","path":"/","params":[],"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"Hello world!"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T15:06:28.286479+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8090/search?q=Paris&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:06:29.544784+02:00","extra":{}}
{"message":"Nominatim API request failed","context":{"error":"Failed to connect to nominatim port 8090 after 0 ms: Couldn't connect to server"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:06:29.545420+02:00","extra":{}}
{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8090 after 0 ms: Couldn't connect to server","context":{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8090 after 0 ms: Couldn't connect to server","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":62,"trace":"#0 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('Paris', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#2 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#3 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#4 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#11 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#12 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#13 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#14 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#16 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#19 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:06:29.545496+02:00","extra":{}}
{"message":"GET /search - 500 Internal Server Error","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:06:29.543","dateEnd":"2025-06-30 15:06:29.545"},"request":{"method":"GET","path":"/search","params":{"q":"Paris","format":"jsonv2"},"body":null},"response":{"statusCode":500,"reasonPhrase":"Internal Server Error","body":"{\n    \"statusCode\": 500,\n    \"error\": {\n        \"error\": \"SERVER_ERROR\",\n        \"description\": \"Failed to connect to Nominatim API: Failed to connect to nominatim port 8090 after 0 ms: Couldn't connect to server\"\n    }\n}"}},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:06:29.545926+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8090/search?q=144+rue+odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:06:40.200574+02:00","extra":{}}
{"message":"Nominatim API request failed","context":{"error":"Failed to connect to nominatim port 8090 after 0 ms: Couldn't connect to server"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:06:40.201205+02:00","extra":{}}
{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8090 after 0 ms: Couldn't connect to server","context":{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8090 after 0 ms: Couldn't connect to server","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":62,"trace":"#0 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#2 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#3 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#4 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#11 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#12 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#13 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#14 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#16 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#19 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:06:40.201296+02:00","extra":{}}
{"message":"GET /search - 500 Internal Server Error","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:06:40.199","dateEnd":"2025-06-30 15:06:40.201"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin","format":"jsonv2"},"body":null},"response":{"statusCode":500,"reasonPhrase":"Internal Server Error","body":"{\n    \"statusCode\": 500,\n    \"error\": {\n        \"error\": \"SERVER_ERROR\",\n        \"description\": \"Failed to connect to Nominatim API: Failed to connect to nominatim port 8090 after 0 ms: Couldn't connect to server\"\n    }\n}"}},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:06:40.201528+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8090/search?q=Paris&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:07:28.715834+02:00","extra":{}}
{"message":"Nominatim API request failed","context":{"error":"Failed to connect to nominatim port 8090 after 0 ms: Couldn't connect to server"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:07:28.716752+02:00","extra":{}}
{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8090 after 0 ms: Couldn't connect to server","context":{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8090 after 0 ms: Couldn't connect to server","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":62,"trace":"#0 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('Paris', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#2 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#3 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#4 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#11 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#12 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#13 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#14 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#16 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#19 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:07:28.716864+02:00","extra":{}}
{"message":"GET /search - 500 Internal Server Error","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:07:28.713","dateEnd":"2025-06-30 15:07:28.717"},"request":{"method":"GET","path":"/search","params":{"q":"Paris","format":"jsonv2"},"body":null},"response":{"statusCode":500,"reasonPhrase":"Internal Server Error","body":"{\n    \"statusCode\": 500,\n    \"error\": {\n        \"error\": \"SERVER_ERROR\",\n        \"description\": \"Failed to connect to Nominatim API: Failed to connect to nominatim port 8090 after 0 ms: Couldn't connect to server\"\n    }\n}"}},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:07:28.717310+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8090/search?q=Paris&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:08:21.443658+02:00","extra":{}}
{"message":"Nominatim API request failed","context":{"error":"Failed to connect to nominatim port 8090 after 0 ms: Couldn't connect to server"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:08:21.444441+02:00","extra":{}}
{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8090 after 0 ms: Couldn't connect to server","context":{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8090 after 0 ms: Couldn't connect to server","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":62,"trace":"#0 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('Paris', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#2 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#3 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#4 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#11 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#12 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#13 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#14 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#16 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#19 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:08:21.444579+02:00","extra":{}}
{"message":"GET /search - 500 Internal Server Error","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:08:21.441","dateEnd":"2025-06-30 15:08:21.445"},"request":{"method":"GET","path":"/search","params":{"q":"Paris","format":"jsonv2"},"body":null},"response":{"statusCode":500,"reasonPhrase":"Internal Server Error","body":"{\n    \"statusCode\": 500,\n    \"error\": {\n        \"error\": \"SERVER_ERROR\",\n        \"description\": \"Failed to connect to Nominatim API: Failed to connect to nominatim port 8090 after 0 ms: Couldn't connect to server\"\n    }\n}"}},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:08:21.445166+02:00","extra":{}}
{"message":"GET /debug/env - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:08:44.263","dateEnd":"2025-06-30 15:08:44.264"},"request":{"method":"GET","path":"/debug/env","params":[],"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"{\n    \"NOMINATIM_BASE_URL\": \"http:\\/\\/nominatim:8090\",\n    \"APP_ENV\": \"dev\"\n}"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T15:08:44.264452+02:00","extra":{}}
{"message":"GET /debug/env - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:09:08.661","dateEnd":"2025-06-30 15:09:08.663"},"request":{"method":"GET","path":"/debug/env","params":[],"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"{\n    \"NOMINATIM_BASE_URL\": \"http:\\/\\/nominatim:8090\",\n    \"APP_ENV\": \"dev\"\n}"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T15:09:08.663868+02:00","extra":{}}
{"message":"GET /debug/env - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:09:40.490","dateEnd":"2025-06-30 15:09:40.492"},"request":{"method":"GET","path":"/debug/env","params":[],"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"{\n    \"NOMINATIM_BASE_URL\": \"http:\\/\\/nominatim:8090\",\n    \"APP_ENV\": \"dev\"\n}"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T15:09:40.492433+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"https://nominatim.openstreetmap.org/search?q=Paris&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:10:07.807281+02:00","extra":{}}
{"message":"Nominatim API request successful","context":{"result_count":4},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:10:08.051366+02:00","extra":{}}
{"message":"GET /search - 200 OK","context":{"kpi":{"executionTime":"1.00s","dateStart":"2025-06-30 15:10:07.804","dateEnd":"2025-06-30 15:10:08.052"},"request":{"method":"GET","path":"/search","params":{"q":"Paris","format":"jsonv2"},"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"[{\"place_id\":88715228,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"relation\",\"osm_id\":7444,\"lat\":\"48.8588897\",\"lon\":\"2.3200410\",\"category\":\"boundary\",\"type\":\"administrative\",\"place_rank\":15,\"importance\":0.8845663630228834,\"addresstype\":\"suburb\",\"name\":\"Paris\",\"display_name\":\"Paris, France m\\u00e9tropolitaine, France\",\"address\":{\"suburb\":\"Paris\",\"city_district\":\"Paris\",\"city\":\"Paris\",\"ISO3166-2-lvl6\":\"FR-75C\",\"region\":\"France m\\u00e9tropolitaine\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"48.8155755\",\"48.9021560\",\"2.2241220\",\"2.4697602\"]},{\"place_id\":88664949,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"relation\",\"osm_id\":71525,\"lat\":\"48.8534951\",\"lon\":\"2.3483915\",\"category\":\"boundary\",\"type\":\"administrative\",\"place_rank\":12,\"importance\":0.8845663630228834,\"addresstype\":\"city\",\"name\":\"Paris\",\"display_name\":\"Paris, \\u00cele-de-France, France m\\u00e9tropolitaine, France\",\"address\":{\"city\":\"Paris\",\"ISO3166-2-lvl6\":\"FR-75C\",\"state\":\"\\u00cele-de-France\",\"ISO3166-2-lvl4\":\"FR-IDF\",\"region\":\"France m\\u00e9tropolitaine\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"48.8155755\",\"48.9021560\",\"2.2241220\",\"2.4697602\"]},{\"place_id\":310325807,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"relation\",\"osm_id\":115357,\"lat\":\"33.6617962\",\"lon\":\"-95.5555130\",\"category\":\"boundary\",\"type\":\"administrative\",\"place_rank\":16,\"importance\":0.5299938695221221,\"addresstype\":\"town\",\"name\":\"Paris\",\"display_name\":\"Paris, Lamar County, Texas, 75460, United States\",\"address\":{\"town\":\"Paris\",\"county\":\"Lamar County\",\"state\":\"Texas\",\"ISO3166-2-lvl4\":\"US-TX\",\"postcode\":\"75460\",\"country\":\"United States\",\"country_code\":\"us\"},\"boundingbox\":[\"33.6206345\",\"33.7383866\",\"-95.6279396\",\"-95.4354115\"]},{\"place_id\":88625937,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"relation\",\"osm_id\":1641193,\"lat\":\"48.8588897\",\"lon\":\"2.3200410\",\"category\":\"boundary\",\"type\":\"administrative\",\"place_rank\":14,\"importance\":0.4533770294842712,\"addresstype\":\"city_district\",\"name\":\"Paris\",\"display_name\":\"Paris, France m\\u00e9tropolitaine, France\",\"address\":{\"city_district\":\"Paris\",\"city\":\"Paris\",\"ISO3166-2-lvl6\":\"FR-75C\",\"region\":\"France m\\u00e9tropolitaine\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"48.8155755\",\"48.9021560\",\"2.2241220\",\"2.4697602\"]}]"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T15:10:08.052371+02:00","extra":{}}
{"message":"GET /search - 400 Bad Request","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:10:15.728","dateEnd":"2025-06-30 15:10:15.729"},"request":{"method":"GET","path":"/search","params":[],"body":null},"response":{"statusCode":400,"reasonPhrase":"Bad Request","body":"{\"error\":\"BAD_REQUEST\",\"description\":\"Bad request\"}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T15:10:15.729609+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"https://nominatim.openstreetmap.org/search?q=London&format=json&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:10:22.085056+02:00","extra":{}}
{"message":"Nominatim API request successful","context":{"result_count":3},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:10:22.219617+02:00","extra":{}}
{"message":"GET /search - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:10:22.084","dateEnd":"2025-06-30 15:10:22.220"},"request":{"method":"GET","path":"/search","params":{"q":"London","format":"json"},"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"[{\"place_id\":258689701,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"relation\",\"osm_id\":65606,\"lat\":\"51.4893335\",\"lon\":\"-0.1440551\",\"class\":\"boundary\",\"type\":\"ceremonial\",\"place_rank\":25,\"importance\":0.8820890292539882,\"addresstype\":\"city\",\"name\":\"London\",\"display_name\":\"London, Greater London, England, United Kingdom\",\"address\":{\"city\":\"London\",\"state_district\":\"Greater London\",\"state\":\"England\",\"ISO3166-2-lvl4\":\"GB-ENG\",\"country\":\"United Kingdom\",\"country_code\":\"gb\"},\"boundingbox\":[\"51.2867601\",\"51.6918741\",\"-0.5103751\",\"0.3340155\"]},{\"place_id\":259350544,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"relation\",\"osm_id\":51800,\"lat\":\"51.5156177\",\"lon\":\"-0.0919983\",\"class\":\"boundary\",\"type\":\"administrative\",\"place_rank\":12,\"importance\":0.656625193199854,\"addresstype\":\"city\",\"name\":\"City of London\",\"display_name\":\"City of London, Greater London, England, United Kingdom\",\"address\":{\"city\":\"City of London\",\"ISO3166-2-lvl6\":\"GB-LND\",\"state_district\":\"Greater London\",\"state\":\"England\",\"ISO3166-2-lvl4\":\"GB-ENG\",\"country\":\"United Kingdom\",\"country_code\":\"gb\"},\"boundingbox\":[\"51.5068709\",\"51.5233122\",\"-0.1138295\",\"-0.0727619\"]},{\"place_id\":396384903,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"relation\",\"osm_id\":7485368,\"lat\":\"42.9832406\",\"lon\":\"-81.2433720\",\"class\":\"boundary\",\"type\":\"administrative\",\"place_rank\":12,\"importance\":0.6078714571941068,\"addresstype\":\"city\",\"name\":\"London\",\"display_name\":\"London, Southwestern Ontario, Ontario, Canada\",\"address\":{\"city\":\"London\",\"state_district\":\"Southwestern Ontario\",\"state\":\"Ontario\",\"ISO3166-2-lvl4\":\"CA-ON\",\"country\":\"Canada\",\"country_code\":\"ca\"},\"boundingbox\":[\"42.8245667\",\"43.0730461\",\"-81.3906556\",\"-81.1070784\"]}]"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T15:10:22.220439+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"https://nominatim.openstreetmap.org/search?q=Berlin&format=jsonv2&limit=2&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:10:27.948266+02:00","extra":{}}
{"message":"Nominatim API request successful","context":{"result_count":1},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:10:28.093705+02:00","extra":{}}
{"message":"GET /search - 200 OK","context":{"kpi":{"executionTime":"1.00s","dateStart":"2025-06-30 15:10:27.947","dateEnd":"2025-06-30 15:10:28.094"},"request":{"method":"GET","path":"/search","params":{"q":"Berlin","limit":"2"},"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"[{\"place_id\":134131805,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"relation\",\"osm_id\":62422,\"lat\":\"52.5108850\",\"lon\":\"13.3989367\",\"category\":\"boundary\",\"type\":\"administrative\",\"place_rank\":7,\"importance\":0.840425236063765,\"addresstype\":\"city\",\"name\":\"Berlin\",\"display_name\":\"Berlin, Deutschland\",\"address\":{\"city\":\"Berlin\",\"ISO3166-2-lvl4\":\"DE-BE\",\"country\":\"Deutschland\",\"country_code\":\"de\"},\"boundingbox\":[\"52.3382448\",\"52.6755087\",\"13.0883450\",\"13.7611609\"]}]"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T15:10:28.094606+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"https://nominatim.openstreetmap.org/search?q=144+rue+odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:10:35.826520+02:00","extra":{}}
{"message":"Nominatim API request successful","context":{"result_count":3},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:10:36.161376+02:00","extra":{}}
{"message":"GET /search - 200 OK","context":{"kpi":{"executionTime":"1.00s","dateStart":"2025-06-30 15:10:35.825","dateEnd":"2025-06-30 15:10:36.162"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin","format":"jsonv2"},"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"[{\"place_id\":77398182,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":999716924,\"lat\":\"43.6157780\",\"lon\":\"3.9149826\",\"category\":\"building\",\"type\":\"yes\",\"place_rank\":30,\"importance\":6.174486915384172e-5,\"addresstype\":\"building\",\"name\":\"Le Mustang\",\"display_name\":\"Le Mustang, 144, Rue d'Odin, Le Mill\\u00e9naire, Mill\\u00e9naire, Port Marianne, Montpellier, H\\u00e9rault, Occitanie, France m\\u00e9tropolitaine, 34000, France\",\"address\":{\"building\":\"Le Mustang\",\"house_number\":\"144\",\"road\":\"Rue d'Odin\",\"neighbourhood\":\"Le Mill\\u00e9naire\",\"suburb\":\"Port Marianne\",\"city\":\"Montpellier\",\"municipality\":\"Montpellier\",\"county\":\"H\\u00e9rault\",\"ISO3166-2-lvl6\":\"FR-34\",\"state\":\"Occitanie\",\"ISO3166-2-lvl4\":\"FR-OCC\",\"region\":\"France m\\u00e9tropolitaine\",\"postcode\":\"34000\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"43.6154811\",\"43.6160648\",\"3.9147473\",\"3.9151402\"]},{\"place_id\":77269695,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"node\",\"osm_id\":4883631518,\"lat\":\"43.6158695\",\"lon\":\"3.9154484\",\"category\":\"place\",\"type\":\"house\",\"place_rank\":30,\"importance\":6.174486915384172e-5,\"addresstype\":\"place\",\"name\":\"\",\"display_name\":\"144, Rue d'Odin, Le Mill\\u00e9naire, Mill\\u00e9naire, Port Marianne, Montpellier, H\\u00e9rault, Occitanie, France m\\u00e9tropolitaine, 34965, France\",\"address\":{\"house_number\":\"144\",\"road\":\"Rue d'Odin\",\"neighbourhood\":\"Le Mill\\u00e9naire\",\"suburb\":\"Port Marianne\",\"city\":\"Montpellier\",\"municipality\":\"Montpellier\",\"county\":\"H\\u00e9rault\",\"ISO3166-2-lvl6\":\"FR-34\",\"state\":\"Occitanie\",\"ISO3166-2-lvl4\":\"FR-OCC\",\"region\":\"France m\\u00e9tropolitaine\",\"postcode\":\"34965\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"43.6158195\",\"43.6159195\",\"3.9153984\",\"3.9154984\"]},{\"place_id\":85295991,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":240772277,\"lat\":\"47.7539687\",\"lon\":\"4.7861422\",\"category\":\"highway\",\"type\":\"residential\",\"place_rank\":26,\"importance\":0.053399159990844554,\"addresstype\":\"road\",\"name\":\"Rue Odin\",\"display_name\":\"Rue Odin, Essarois, Montbard, C\\u00f4te-d'Or, Bourgogne-Franche-Comt\\u00e9, France m\\u00e9tropolitaine, 21290, France\",\"address\":{\"road\":\"Rue Odin\",\"village\":\"Essarois\",\"municipality\":\"Montbard\",\"county\":\"C\\u00f4te-d'Or\",\"ISO3166-2-lvl6\":\"FR-21\",\"state\":\"Bourgogne-Franche-Comt\\u00e9\",\"ISO3166-2-lvl4\":\"FR-BFC\",\"region\":\"France m\\u00e9tropolitaine\",\"postcode\":\"21290\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"47.7539535\",\"47.7541767\",\"4.7856159\",\"4.7867013\"]}]"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T15:10:36.162204+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8080/search?q=144+rue+odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:13:30.172544+02:00","extra":{}}
{"message":"Nominatim API request failed","context":{"error":"Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:30.173134+02:00","extra":{}}
{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","context":{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":62,"trace":"#0 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#2 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#3 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#4 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#11 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#12 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#13 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#14 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#16 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#19 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:30.173200+02:00","extra":{}}
{"message":"GET /search - 500 Internal Server Error","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:13:30.169","dateEnd":"2025-06-30 15:13:30.173"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin"},"body":null},"response":{"statusCode":500,"reasonPhrase":"Internal Server Error","body":"{\n    \"statusCode\": 500,\n    \"error\": {\n        \"error\": \"SERVER_ERROR\",\n        \"description\": \"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server\"\n    }\n}"}},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:30.173540+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8080/search?q=144+rue+odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:13:35.442165+02:00","extra":{}}
{"message":"Nominatim API request failed","context":{"error":"Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:35.442800+02:00","extra":{}}
{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","context":{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":62,"trace":"#0 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#2 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#3 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#4 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#11 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#12 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#13 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#14 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#16 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#19 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:35.442903+02:00","extra":{}}
{"message":"GET /search - 500 Internal Server Error","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:13:35.441","dateEnd":"2025-06-30 15:13:35.443"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin"},"body":null},"response":{"statusCode":500,"reasonPhrase":"Internal Server Error","body":"{\n    \"statusCode\": 500,\n    \"error\": {\n        \"error\": \"SERVER_ERROR\",\n        \"description\": \"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server\"\n    }\n}"}},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:35.443144+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8080/search?q=144+rue+odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:13:36.189487+02:00","extra":{}}
{"message":"Nominatim API request failed","context":{"error":"Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:36.190114+02:00","extra":{}}
{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","context":{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":62,"trace":"#0 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#2 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#3 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#4 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#11 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#12 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#13 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#14 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#16 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#19 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:36.190200+02:00","extra":{}}
{"message":"GET /search - 500 Internal Server Error","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:13:36.188","dateEnd":"2025-06-30 15:13:36.190"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin"},"body":null},"response":{"statusCode":500,"reasonPhrase":"Internal Server Error","body":"{\n    \"statusCode\": 500,\n    \"error\": {\n        \"error\": \"SERVER_ERROR\",\n        \"description\": \"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server\"\n    }\n}"}},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:36.190406+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8080/search?q=144+rue+odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:13:37.009562+02:00","extra":{}}
{"message":"Nominatim API request failed","context":{"error":"Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:37.010487+02:00","extra":{}}
{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","context":{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":62,"trace":"#0 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#2 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#3 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#4 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#11 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#12 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#13 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#14 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#16 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#19 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:37.010624+02:00","extra":{}}
{"message":"GET /search - 500 Internal Server Error","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:13:37.008","dateEnd":"2025-06-30 15:13:37.010"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin"},"body":null},"response":{"statusCode":500,"reasonPhrase":"Internal Server Error","body":"{\n    \"statusCode\": 500,\n    \"error\": {\n        \"error\": \"SERVER_ERROR\",\n        \"description\": \"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server\"\n    }\n}"}},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:37.010917+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8080/search?q=144+rue+odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:13:37.532787+02:00","extra":{}}
{"message":"Nominatim API request failed","context":{"error":"Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:37.533544+02:00","extra":{}}
{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","context":{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":62,"trace":"#0 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#2 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#3 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#4 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#11 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#12 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#13 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#14 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#16 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#19 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:37.533682+02:00","extra":{}}
{"message":"GET /search - 500 Internal Server Error","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:13:37.532","dateEnd":"2025-06-30 15:13:37.533"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin"},"body":null},"response":{"statusCode":500,"reasonPhrase":"Internal Server Error","body":"{\n    \"statusCode\": 500,\n    \"error\": {\n        \"error\": \"SERVER_ERROR\",\n        \"description\": \"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server\"\n    }\n}"}},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:37.533985+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8080/search?q=144+rue+odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:13:38.554953+02:00","extra":{}}
{"message":"Nominatim API request failed","context":{"error":"Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:38.555456+02:00","extra":{}}
{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","context":{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":62,"trace":"#0 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#2 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#3 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#4 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#11 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#12 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#13 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#14 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#16 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#19 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:38.555523+02:00","extra":{}}
{"message":"GET /search - 500 Internal Server Error","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:13:38.554","dateEnd":"2025-06-30 15:13:38.555"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin"},"body":null},"response":{"statusCode":500,"reasonPhrase":"Internal Server Error","body":"{\n    \"statusCode\": 500,\n    \"error\": {\n        \"error\": \"SERVER_ERROR\",\n        \"description\": \"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server\"\n    }\n}"}},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:38.555709+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8080/search?q=144+rue+odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:13:40.737747+02:00","extra":{}}
{"message":"Nominatim API request failed","context":{"error":"Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:40.738239+02:00","extra":{}}
{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","context":{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":62,"trace":"#0 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#2 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#3 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#4 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#11 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#12 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#13 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#14 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#16 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#19 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:40.738326+02:00","extra":{}}
{"message":"GET /search - 500 Internal Server Error","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:13:40.737","dateEnd":"2025-06-30 15:13:40.738"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin"},"body":null},"response":{"statusCode":500,"reasonPhrase":"Internal Server Error","body":"{\n    \"statusCode\": 500,\n    \"error\": {\n        \"error\": \"SERVER_ERROR\",\n        \"description\": \"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server\"\n    }\n}"}},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:40.738561+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8080/search?q=144+rue+odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:13:41.569751+02:00","extra":{}}
{"message":"Nominatim API request failed","context":{"error":"Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:41.570361+02:00","extra":{}}
{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","context":{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":62,"trace":"#0 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#2 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#3 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#4 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#11 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#12 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#13 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#14 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#16 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#19 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:41.570446+02:00","extra":{}}
{"message":"GET /search - 500 Internal Server Error","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:13:41.568","dateEnd":"2025-06-30 15:13:41.570"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin"},"body":null},"response":{"statusCode":500,"reasonPhrase":"Internal Server Error","body":"{\n    \"statusCode\": 500,\n    \"error\": {\n        \"error\": \"SERVER_ERROR\",\n        \"description\": \"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server\"\n    }\n}"}},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:41.570674+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8080/search?q=144+rue+odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:13:42.271854+02:00","extra":{}}
{"message":"Nominatim API request failed","context":{"error":"Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:42.272393+02:00","extra":{}}
{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","context":{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":62,"trace":"#0 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#2 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#3 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#4 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#11 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#12 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#13 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#14 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#16 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#19 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:42.272463+02:00","extra":{}}
{"message":"GET /search - 500 Internal Server Error","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:13:42.271","dateEnd":"2025-06-30 15:13:42.272"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin"},"body":null},"response":{"statusCode":500,"reasonPhrase":"Internal Server Error","body":"{\n    \"statusCode\": 500,\n    \"error\": {\n        \"error\": \"SERVER_ERROR\",\n        \"description\": \"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server\"\n    }\n}"}},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:42.272665+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8080/search?q=144+rue+odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:13:43.758424+02:00","extra":{}}
{"message":"Nominatim API request failed","context":{"error":"Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:43.758965+02:00","extra":{}}
{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","context":{"message":"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server","file":"/var/www/src/Infrastructure/Search/NominatimSearchService.php","line":62,"trace":"#0 /var/www/src/Domain/Search/UseCase/FreeFormSearch/FreeFormSearchUseCase.php(16): App\\Infrastructure\\Search\\NominatimSearchService->freeFormSearch('144 rue odin', 'jsonv2', 10, true, false, false)\n#1 /var/www/src/Actions/Search/SearchAction.php(34): App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchUseCase->search(Object(App\\Domain\\Search\\UseCase\\FreeFormSearch\\FreeFormSearchRequest))\n#2 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\Search\\SearchAction->action()\n#3 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#4 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\Search\\SearchAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#5 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#8 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#10 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#11 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#12 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#13 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#14 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#16 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#18 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#19 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:43.759045+02:00","extra":{}}
{"message":"GET /search - 500 Internal Server Error","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:13:43.757","dateEnd":"2025-06-30 15:13:43.759"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin"},"body":null},"response":{"statusCode":500,"reasonPhrase":"Internal Server Error","body":"{\n    \"statusCode\": 500,\n    \"error\": {\n        \"error\": \"SERVER_ERROR\",\n        \"description\": \"Failed to connect to Nominatim API: Failed to connect to nominatim port 8080 after 0 ms: Couldn't connect to server\"\n    }\n}"}},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T15:13:43.759215+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8080/search?q=144+rue+odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:13:45.529108+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8080/search?q=144+rue+odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:13:46.165886+02:00","extra":{}}
{"message":"Nominatim API request successful","context":{"result_count":2},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:13:47.686657+02:00","extra":{}}
{"message":"GET /search - 200 OK","context":{"kpi":{"executionTime":"2.00s","dateStart":"2025-06-30 15:13:45.528","dateEnd":"2025-06-30 15:13:47.687"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin"},"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"[{\"place_id\":725005,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":999716924,\"lat\":\"43.615778\",\"lon\":\"3.9149826431045973\",\"category\":\"building\",\"type\":\"yes\",\"place_rank\":30,\"importance\":9.99999999995449e-6,\"addresstype\":\"building\",\"name\":\"Le Mustang\",\"display_name\":\"Le Mustang, 144, Rue d'Odin, Le Mill\\u00e9naire, Mill\\u00e9naire, Port Marianne, Montpellier, H\\u00e9rault, 34000, France\",\"address\":{\"building\":\"Le Mustang\",\"house_number\":\"144\",\"road\":\"Rue d'Odin\",\"neighbourhood\":\"Le Mill\\u00e9naire\",\"suburb\":\"Port Marianne\",\"city\":\"Montpellier\",\"municipality\":\"Montpellier\",\"county\":\"H\\u00e9rault\",\"ISO3166-2-lvl6\":\"FR-34\",\"postcode\":\"34000\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"43.6154811\",\"43.6160648\",\"3.9147473\",\"3.9151402\"]},{\"place_id\":720277,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"node\",\"osm_id\":4883631518,\"lat\":\"43.6158695\",\"lon\":\"3.9154484\",\"category\":\"place\",\"type\":\"house\",\"place_rank\":30,\"importance\":9.99999999995449e-6,\"addresstype\":\"place\",\"name\":\"\",\"display_name\":\"144, Rue d'Odin, Le Mill\\u00e9naire, Mill\\u00e9naire, Port Marianne, Montpellier, H\\u00e9rault, 34965, France\",\"address\":{\"house_number\":\"144\",\"road\":\"Rue d'Odin\",\"neighbourhood\":\"Le Mill\\u00e9naire\",\"suburb\":\"Port Marianne\",\"city\":\"Montpellier\",\"municipality\":\"Montpellier\",\"county\":\"H\\u00e9rault\",\"ISO3166-2-lvl6\":\"FR-34\",\"postcode\":\"34965\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"43.6158195\",\"43.6159195\",\"3.9153984\",\"3.9154984\"]}]"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T15:13:47.687049+02:00","extra":{}}
{"message":"Nominatim API request successful","context":{"result_count":2},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:13:47.710727+02:00","extra":{}}
{"message":"GET /search - 200 OK","context":{"kpi":{"executionTime":"1.00s","dateStart":"2025-06-30 15:13:46.165","dateEnd":"2025-06-30 15:13:47.710"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin"},"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"[{\"place_id\":725005,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":999716924,\"lat\":\"43.615778\",\"lon\":\"3.9149826431045973\",\"category\":\"building\",\"type\":\"yes\",\"place_rank\":30,\"importance\":9.99999999995449e-6,\"addresstype\":\"building\",\"name\":\"Le Mustang\",\"display_name\":\"Le Mustang, 144, Rue d'Odin, Le Mill\\u00e9naire, Mill\\u00e9naire, Port Marianne, Montpellier, H\\u00e9rault, 34000, France\",\"address\":{\"building\":\"Le Mustang\",\"house_number\":\"144\",\"road\":\"Rue d'Odin\",\"neighbourhood\":\"Le Mill\\u00e9naire\",\"suburb\":\"Port Marianne\",\"city\":\"Montpellier\",\"municipality\":\"Montpellier\",\"county\":\"H\\u00e9rault\",\"ISO3166-2-lvl6\":\"FR-34\",\"postcode\":\"34000\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"43.6154811\",\"43.6160648\",\"3.9147473\",\"3.9151402\"]},{\"place_id\":720277,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"node\",\"osm_id\":4883631518,\"lat\":\"43.6158695\",\"lon\":\"3.9154484\",\"category\":\"place\",\"type\":\"house\",\"place_rank\":30,\"importance\":9.99999999995449e-6,\"addresstype\":\"place\",\"name\":\"\",\"display_name\":\"144, Rue d'Odin, Le Mill\\u00e9naire, Mill\\u00e9naire, Port Marianne, Montpellier, H\\u00e9rault, 34965, France\",\"address\":{\"house_number\":\"144\",\"road\":\"Rue d'Odin\",\"neighbourhood\":\"Le Mill\\u00e9naire\",\"suburb\":\"Port Marianne\",\"city\":\"Montpellier\",\"municipality\":\"Montpellier\",\"county\":\"H\\u00e9rault\",\"ISO3166-2-lvl6\":\"FR-34\",\"postcode\":\"34965\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"43.6158195\",\"43.6159195\",\"3.9153984\",\"3.9154984\"]}]"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T15:13:47.711020+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8080/search?q=144+rue+odin&format=jsonv2&limit=1&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:14:07.431105+02:00","extra":{}}
{"message":"Nominatim API request successful","context":{"result_count":1},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:14:07.449362+02:00","extra":{}}
{"message":"GET /search - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:14:07.430","dateEnd":"2025-06-30 15:14:07.449"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin","limit":"1"},"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"[{\"place_id\":725005,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":999716924,\"lat\":\"43.615778\",\"lon\":\"3.9149826431045973\",\"category\":\"building\",\"type\":\"yes\",\"place_rank\":30,\"importance\":9.99999999995449e-6,\"addresstype\":\"building\",\"name\":\"Le Mustang\",\"display_name\":\"Le Mustang, 144, Rue d'Odin, Le Mill\\u00e9naire, Mill\\u00e9naire, Port Marianne, Montpellier, H\\u00e9rault, 34000, France\",\"address\":{\"building\":\"Le Mustang\",\"house_number\":\"144\",\"road\":\"Rue d'Odin\",\"neighbourhood\":\"Le Mill\\u00e9naire\",\"suburb\":\"Port Marianne\",\"city\":\"Montpellier\",\"municipality\":\"Montpellier\",\"county\":\"H\\u00e9rault\",\"ISO3166-2-lvl6\":\"FR-34\",\"postcode\":\"34000\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"43.6154811\",\"43.6160648\",\"3.9147473\",\"3.9151402\"]}]"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T15:14:07.449652+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8080/search?q=144+rue+odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:55:59.924080+02:00","extra":{}}
{"message":"Nominatim API request successful","context":{"result_count":2},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:55:59.943182+02:00","extra":{}}
{"message":"GET /search - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:55:59.922","dateEnd":"2025-06-30 15:55:59.943"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin","format":"jsonv2"},"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"[{\"place_id\":725005,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":999716924,\"lat\":\"43.615778\",\"lon\":\"3.9149826431045973\",\"category\":\"building\",\"type\":\"yes\",\"place_rank\":30,\"importance\":9.99999999995449e-6,\"addresstype\":\"building\",\"name\":\"Le Mustang\",\"display_name\":\"Le Mustang, 144, Rue d'Odin, Le Mill\\u00e9naire, Mill\\u00e9naire, Port Marianne, Montpellier, H\\u00e9rault, 34000, France\",\"address\":{\"building\":\"Le Mustang\",\"house_number\":\"144\",\"road\":\"Rue d'Odin\",\"neighbourhood\":\"Le Mill\\u00e9naire\",\"suburb\":\"Port Marianne\",\"city\":\"Montpellier\",\"municipality\":\"Montpellier\",\"county\":\"H\\u00e9rault\",\"ISO3166-2-lvl6\":\"FR-34\",\"postcode\":\"34000\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"43.6154811\",\"43.6160648\",\"3.9147473\",\"3.9151402\"]},{\"place_id\":720277,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"node\",\"osm_id\":4883631518,\"lat\":\"43.6158695\",\"lon\":\"3.9154484\",\"category\":\"place\",\"type\":\"house\",\"place_rank\":30,\"importance\":9.99999999995449e-6,\"addresstype\":\"place\",\"name\":\"\",\"display_name\":\"144, Rue d'Odin, Le Mill\\u00e9naire, Mill\\u00e9naire, Port Marianne, Montpellier, H\\u00e9rault, 34965, France\",\"address\":{\"house_number\":\"144\",\"road\":\"Rue d'Odin\",\"neighbourhood\":\"Le Mill\\u00e9naire\",\"suburb\":\"Port Marianne\",\"city\":\"Montpellier\",\"municipality\":\"Montpellier\",\"county\":\"H\\u00e9rault\",\"ISO3166-2-lvl6\":\"FR-34\",\"postcode\":\"34965\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"43.6158195\",\"43.6159195\",\"3.9153984\",\"3.9154984\"]}]"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T15:55:59.943470+02:00","extra":{}}
{"message":"Making Nominatim API request","context":{"url":"http://nominatim:8080/search?q=144+rue+odin&format=jsonv2&limit=10&addressdetails=1&extratags=0&namedetails=0"},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:56:05.768452+02:00","extra":{}}
{"message":"Nominatim API request successful","context":{"result_count":2},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T15:56:06.160316+02:00","extra":{}}
{"message":"GET /search - 200 OK","context":{"kpi":{"executionTime":"1.00s","dateStart":"2025-06-30 15:56:05.767","dateEnd":"2025-06-30 15:56:06.160"},"request":{"method":"GET","path":"/search","params":{"q":"144 rue odin"},"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"[{\"place_id\":725005,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"way\",\"osm_id\":999716924,\"lat\":\"43.615778\",\"lon\":\"3.9149826431045973\",\"category\":\"building\",\"type\":\"yes\",\"place_rank\":30,\"importance\":9.99999999995449e-6,\"addresstype\":\"building\",\"name\":\"Le Mustang\",\"display_name\":\"Le Mustang, 144, Rue d'Odin, Le Mill\\u00e9naire, Mill\\u00e9naire, Port Marianne, Montpellier, H\\u00e9rault, 34000, France\",\"address\":{\"building\":\"Le Mustang\",\"house_number\":\"144\",\"road\":\"Rue d'Odin\",\"neighbourhood\":\"Le Mill\\u00e9naire\",\"suburb\":\"Port Marianne\",\"city\":\"Montpellier\",\"municipality\":\"Montpellier\",\"county\":\"H\\u00e9rault\",\"ISO3166-2-lvl6\":\"FR-34\",\"postcode\":\"34000\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"43.6154811\",\"43.6160648\",\"3.9147473\",\"3.9151402\"]},{\"place_id\":720277,\"licence\":\"Data \\u00a9 OpenStreetMap contributors, ODbL 1.0. http:\\/\\/osm.org\\/copyright\",\"osm_type\":\"node\",\"osm_id\":4883631518,\"lat\":\"43.6158695\",\"lon\":\"3.9154484\",\"category\":\"place\",\"type\":\"house\",\"place_rank\":30,\"importance\":9.99999999995449e-6,\"addresstype\":\"place\",\"name\":\"\",\"display_name\":\"144, Rue d'Odin, Le Mill\\u00e9naire, Mill\\u00e9naire, Port Marianne, Montpellier, H\\u00e9rault, 34965, France\",\"address\":{\"house_number\":\"144\",\"road\":\"Rue d'Odin\",\"neighbourhood\":\"Le Mill\\u00e9naire\",\"suburb\":\"Port Marianne\",\"city\":\"Montpellier\",\"municipality\":\"Montpellier\",\"county\":\"H\\u00e9rault\",\"ISO3166-2-lvl6\":\"FR-34\",\"postcode\":\"34965\",\"country\":\"France\",\"country_code\":\"fr\"},\"boundingbox\":[\"43.6158195\",\"43.6159195\",\"3.9153984\",\"3.9154984\"]}]"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T15:56:06.160591+02:00","extra":{}}
{"message":"GET /search - 400 Bad Request","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 15:56:21.568","dateEnd":"2025-06-30 15:56:21.569"},"request":{"method":"GET","path":"/search","params":{"city":"144 rue odin"},"body":null},"response":{"statusCode":400,"reasonPhrase":"Bad Request","body":"{\"error\":\"BAD_REQUEST\",\"description\":\"Bad request\"}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T15:56:21.569300+02:00","extra":{}}
{"message":"Connecting with parameters {params}","context":{"params":{"driver":"pdo_mysql","host":"maria_db","dbname":"nominatim","user":"nominatim","password":"<redacted>","charset":"utf8mb4"}},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:26:53.461743+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT id_user, username, created_at, updated_at FROM user"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:26:53.464796+02:00","extra":{}}
{"message":"An exception occurred while executing a query: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'nominatim.user' doesn't exist","context":{"message":"An exception occurred while executing a query: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'nominatim.user' doesn't exist","file":"/var/www/vendor/doctrine/dbal/src/Driver/API/MySQL/ExceptionConverter.php","line":40,"trace":"#0 /var/www/vendor/doctrine/dbal/src/Connection.php(1418): Doctrine\\DBAL\\Driver\\API\\MySQL\\ExceptionConverter->convert(Object(Doctrine\\DBAL\\Driver\\PDO\\Exception), Object(Doctrine\\DBAL\\Query))\n#1 /var/www/vendor/doctrine/dbal/src/Connection.php(1360): Doctrine\\DBAL\\Connection->handleDriverException(Object(Doctrine\\DBAL\\Driver\\PDO\\Exception), Object(Doctrine\\DBAL\\Query))\n#2 /var/www/vendor/doctrine/dbal/src/Connection.php(787): Doctrine\\DBAL\\Connection->convertExceptionDuringQuery(Object(Doctrine\\DBAL\\Driver\\PDO\\Exception), 'SELECT id_user,...', Array, Array)\n#3 /var/www/vendor/doctrine/dbal/src/Query/QueryBuilder.php(305): Doctrine\\DBAL\\Connection->executeQuery('SELECT id_user,...', Array, Array, NULL)\n#4 /var/www/src/Infrastructure/Persistence/Domain/User/DoctrineUserRepository.php(23): Doctrine\\DBAL\\Query\\QueryBuilder->executeQuery()\n#5 /var/www/src/Domain/User/UseCase/ListUsers/ListUsersUseCase.php(17): App\\Infrastructure\\Persistence\\Domain\\User\\DoctrineUserRepository->findAll()\n#6 /var/www/src/Actions/User/ListUsersAction.php(21): App\\Domain\\User\\UseCase\\ListUsers\\ListUsersUseCase->getAllUsers(Object(App\\Domain\\User\\UseCase\\ListUsers\\ListUsersRequest))\n#7 /var/www/src/Application/Action/AbstractAction.php(29): App\\Actions\\User\\ListUsersAction->action()\n#8 /var/www/vendor/slim/slim/Slim/Handlers/Strategies/RequestResponse.php(38): App\\Application\\Action\\AbstractAction->__invoke(Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#9 /var/www/vendor/slim/slim/Slim/Routing/Route.php(363): Slim\\Handlers\\Strategies\\RequestResponse->__invoke(Object(App\\Actions\\User\\ListUsersAction), Object(Nyholm\\Psr7\\ServerRequest), Object(Nyholm\\Psr7\\Response), Array)\n#10 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\Routing\\Route->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#11 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#12 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#13 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#14 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#15 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#16 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#17 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#18 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#19 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#20 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#21 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#22 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#23 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#24 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T16:26:53.466160+02:00","extra":{}}
{"message":"GET /users - 500 Internal Server Error","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 16:26:53.452","dateEnd":"2025-06-30 16:26:53.466"},"request":{"method":"GET","path":"/users","params":[],"body":null},"response":{"statusCode":500,"reasonPhrase":"Internal Server Error","body":"{\n    \"statusCode\": 500,\n    \"error\": {\n        \"error\": \"SERVER_ERROR\",\n        \"description\": \"An exception occurred while executing a query: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'nominatim.user' doesn't exist\"\n    }\n}"}},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T16:26:53.466566+02:00","extra":{}}
{"message":"Disconnecting","context":{},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:26:53.466839+02:00","extra":{}}
{"message":"Connecting with parameters {params}","context":{"params":{"driver":"pdo_mysql","host":"maria_db","dbname":"nominatim","user":"nominatim","password":"<redacted>","charset":"utf8mb4"}},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:27:00.578381+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT DATABASE()"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.579420+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT DATABASE()"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.583014+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT TABLE_NAME\nFROM information_schema.TABLES\nWHERE TABLE_SCHEMA = ?\n  AND TABLE_TYPE = 'BASE TABLE'\nORDER BY TABLE_NAME","params":{"1":"nominatim"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.583273+02:00","extra":{}}
{"message":"Executing statement: {sql}","context":{"sql":"CREATE TABLE doctrine_migration_versions (version VARCHAR(191) NOT NULL, executed_at DATETIME DEFAULT NULL, execution_time INT DEFAULT NULL, PRIMARY KEY(version))"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.590624+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT * FROM doctrine_migration_versions"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.598017+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT * FROM doctrine_migration_versions"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.599699+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT * FROM doctrine_migration_versions"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.599900+02:00","extra":{}}
{"message":"Beginning transaction","context":{},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.601723+02:00","extra":{}}
{"message":"Executing statement: {sql}","context":{"sql":"SAVEPOINT DOCTRINE_2"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.602148+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT DATABASE()"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.603686+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT c.TABLE_NAME,       c.COLUMN_NAME        AS field,\n           IF(\n        c.COLUMN_TYPE = 'longtext'\n        AND EXISTS(\n            SELECT * FROM information_schema.CHECK_CONSTRAINTS i_c\n            WHERE i_c.CONSTRAINT_SCHEMA = 'nominatim'\n            AND i_c.TABLE_NAME = c.TABLE_NAME\n            AND i_c.CHECK_CLAUSE = CONCAT(\n                'json_valid(`',\n                    c.COLUMN_NAME,\n                '`)'\n            )\n        ),\n        'json',\n        c.COLUMN_TYPE\n    )       AS type,\n       c.IS_NULLABLE        AS `null`,\n       c.COLUMN_KEY         AS `key`,\n       c.COLUMN_DEFAULT     AS `default`,\n       c.EXTRA,\n       c.COLUMN_COMMENT     AS comment,\n       c.CHARACTER_SET_NAME AS characterset,\n       c.COLLATION_NAME     AS collation\nFROM information_schema.COLUMNS c\n    INNER JOIN information_schema.TABLES t\n        ON t.TABLE_NAME = c.TABLE_NAME WHERE c.TABLE_SCHEMA = ? AND t.TABLE_SCHEMA = ? AND t.TABLE_TYPE = 'BASE TABLE' ORDER BY ORDINAL_POSITION","params":{"1":"nominatim","2":"nominatim"},"types":{"1":0,"2":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.603832+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT TABLE_NAME,        NON_UNIQUE  AS Non_Unique,\n        INDEX_NAME  AS Key_name,\n        COLUMN_NAME AS Column_Name,\n        SUB_PART    AS Sub_Part,\n        INDEX_TYPE  AS Index_Type\nFROM information_schema.STATISTICS WHERE TABLE_SCHEMA = ? ORDER BY SEQ_IN_INDEX","params":{"1":"nominatim"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.605604+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT DISTINCT k.TABLE_NAME,            k.CONSTRAINT_NAME,\n            k.COLUMN_NAME,\n            k.REFERENCED_TABLE_NAME,\n            k.REFERENCED_COLUMN_NAME,\n            k.ORDINAL_POSITION,\n            c.UPDATE_RULE,\n            c.DELETE_RULE\nFROM information_schema.key_column_usage k\nINNER JOIN information_schema.referential_constraints c\nON c.CONSTRAINT_NAME = k.CONSTRAINT_NAME\nAND c.TABLE_NAME = k.TABLE_NAME WHERE k.TABLE_SCHEMA = ? AND c.CONSTRAINT_SCHEMA = ? AND k.REFERENCED_COLUMN_NAME IS NOT NULL ORDER BY k.ORDINAL_POSITION","params":{"1":"nominatim","2":"nominatim"},"types":{"1":0,"2":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.605865+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"    SELECT t.TABLE_NAME,\n           t.ENGINE,\n           t.AUTO_INCREMENT,\n           t.TABLE_COMMENT,\n           t.CREATE_OPTIONS,\n           t.TABLE_COLLATION,\n           ccsa.CHARACTER_SET_NAME\n      FROM information_schema.TABLES t\n        INNER JOIN information_schema.COLLATION_CHARACTER_SET_APPLICABILITY ccsa\n          ON ccsa.FULL_COLLATION_NAME = t.TABLE_COLLATION WHERE t.TABLE_SCHEMA = ? AND t.TABLE_TYPE = 'BASE TABLE'","params":{"1":"nominatim"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.606238+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT @@character_set_database, @@collation_database"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.608254+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT DATABASE()"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.608441+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT c.TABLE_NAME,       c.COLUMN_NAME        AS field,\n           IF(\n        c.COLUMN_TYPE = 'longtext'\n        AND EXISTS(\n            SELECT * FROM information_schema.CHECK_CONSTRAINTS i_c\n            WHERE i_c.CONSTRAINT_SCHEMA = 'nominatim'\n            AND i_c.TABLE_NAME = c.TABLE_NAME\n            AND i_c.CHECK_CLAUSE = CONCAT(\n                'json_valid(`',\n                    c.COLUMN_NAME,\n                '`)'\n            )\n        ),\n        'json',\n        c.COLUMN_TYPE\n    )       AS type,\n       c.IS_NULLABLE        AS `null`,\n       c.COLUMN_KEY         AS `key`,\n       c.COLUMN_DEFAULT     AS `default`,\n       c.EXTRA,\n       c.COLUMN_COMMENT     AS comment,\n       c.CHARACTER_SET_NAME AS characterset,\n       c.COLLATION_NAME     AS collation\nFROM information_schema.COLUMNS c\n    INNER JOIN information_schema.TABLES t\n        ON t.TABLE_NAME = c.TABLE_NAME WHERE c.TABLE_SCHEMA = ? AND t.TABLE_SCHEMA = ? AND t.TABLE_TYPE = 'BASE TABLE' ORDER BY ORDINAL_POSITION","params":{"1":"nominatim","2":"nominatim"},"types":{"1":0,"2":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.608546+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT TABLE_NAME,        NON_UNIQUE  AS Non_Unique,\n        INDEX_NAME  AS Key_name,\n        COLUMN_NAME AS Column_Name,\n        SUB_PART    AS Sub_Part,\n        INDEX_TYPE  AS Index_Type\nFROM information_schema.STATISTICS WHERE TABLE_SCHEMA = ? ORDER BY SEQ_IN_INDEX","params":{"1":"nominatim"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.609394+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT DISTINCT k.TABLE_NAME,            k.CONSTRAINT_NAME,\n            k.COLUMN_NAME,\n            k.REFERENCED_TABLE_NAME,\n            k.REFERENCED_COLUMN_NAME,\n            k.ORDINAL_POSITION,\n            c.UPDATE_RULE,\n            c.DELETE_RULE\nFROM information_schema.key_column_usage k\nINNER JOIN information_schema.referential_constraints c\nON c.CONSTRAINT_NAME = k.CONSTRAINT_NAME\nAND c.TABLE_NAME = k.TABLE_NAME WHERE k.TABLE_SCHEMA = ? AND c.CONSTRAINT_SCHEMA = ? AND k.REFERENCED_COLUMN_NAME IS NOT NULL ORDER BY k.ORDINAL_POSITION","params":{"1":"nominatim","2":"nominatim"},"types":{"1":0,"2":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.609603+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"    SELECT t.TABLE_NAME,\n           t.ENGINE,\n           t.AUTO_INCREMENT,\n           t.TABLE_COMMENT,\n           t.CREATE_OPTIONS,\n           t.TABLE_COLLATION,\n           ccsa.CHARACTER_SET_NAME\n      FROM information_schema.TABLES t\n        INNER JOIN information_schema.COLLATION_CHARACTER_SET_APPLICABILITY ccsa\n          ON ccsa.FULL_COLLATION_NAME = t.TABLE_COLLATION WHERE t.TABLE_SCHEMA = ? AND t.TABLE_TYPE = 'BASE TABLE'","params":{"1":"nominatim"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.609844+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"CREATE TABLE user (id_user INT UNSIGNED AUTO_INCREMENT NOT NULL, username VARCHAR(128) NOT NULL, password VARCHAR(255) NOT NULL, PRIMARY KEY(id_user)) DEFAULT CHARACTER SET utf8mb4"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.611422+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"CREATE TABLE access_token (token VARCHAR(255) NOT NULL, id_user INT UNSIGNED DEFAULT NULL, client VARCHAR(128) NOT NULL, date_expiration DATETIME NOT NULL, INDEX IDX_B6A2DD686B3CA4B (id_user), PRIMARY KEY(token)) DEFAULT CHARACTER SET utf8mb4"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.615645+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"ALTER TABLE access_token ADD CONSTRAINT FK_B6A2DD686B3CA4B FOREIGN KEY (id_user) REFERENCES user (id_user) ON UPDATE CASCADE ON DELETE CASCADE"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.620008+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"INSERT INTO doctrine_migration_versions (version, executed_at, execution_time) VALUES (?, ?, ?)","params":{"1":"App\\Infrastructure\\Doctrine\\Migrations\\Version20240223075442","2":"2025-06-30 16:27:00","3":27},"types":{"1":0,"2":0,"3":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.630581+02:00","extra":{}}
{"message":"Executing statement: {sql}","context":{"sql":"SAVEPOINT DOCTRINE_3"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.631509+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT DATABASE()"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.631645+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT c.TABLE_NAME,       c.COLUMN_NAME        AS field,\n           IF(\n        c.COLUMN_TYPE = 'longtext'\n        AND EXISTS(\n            SELECT * FROM information_schema.CHECK_CONSTRAINTS i_c\n            WHERE i_c.CONSTRAINT_SCHEMA = 'nominatim'\n            AND i_c.TABLE_NAME = c.TABLE_NAME\n            AND i_c.CHECK_CLAUSE = CONCAT(\n                'json_valid(`',\n                    c.COLUMN_NAME,\n                '`)'\n            )\n        ),\n        'json',\n        c.COLUMN_TYPE\n    )       AS type,\n       c.IS_NULLABLE        AS `null`,\n       c.COLUMN_KEY         AS `key`,\n       c.COLUMN_DEFAULT     AS `default`,\n       c.EXTRA,\n       c.COLUMN_COMMENT     AS comment,\n       c.CHARACTER_SET_NAME AS characterset,\n       c.COLLATION_NAME     AS collation\nFROM information_schema.COLUMNS c\n    INNER JOIN information_schema.TABLES t\n        ON t.TABLE_NAME = c.TABLE_NAME WHERE c.TABLE_SCHEMA = ? AND t.TABLE_SCHEMA = ? AND t.TABLE_TYPE = 'BASE TABLE' ORDER BY ORDINAL_POSITION","params":{"1":"nominatim","2":"nominatim"},"types":{"1":0,"2":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.631765+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT TABLE_NAME,        NON_UNIQUE  AS Non_Unique,\n        INDEX_NAME  AS Key_name,\n        COLUMN_NAME AS Column_Name,\n        SUB_PART    AS Sub_Part,\n        INDEX_TYPE  AS Index_Type\nFROM information_schema.STATISTICS WHERE TABLE_SCHEMA = ? ORDER BY SEQ_IN_INDEX","params":{"1":"nominatim"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.632699+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT DISTINCT k.TABLE_NAME,            k.CONSTRAINT_NAME,\n            k.COLUMN_NAME,\n            k.REFERENCED_TABLE_NAME,\n            k.REFERENCED_COLUMN_NAME,\n            k.ORDINAL_POSITION,\n            c.UPDATE_RULE,\n            c.DELETE_RULE\nFROM information_schema.key_column_usage k\nINNER JOIN information_schema.referential_constraints c\nON c.CONSTRAINT_NAME = k.CONSTRAINT_NAME\nAND c.TABLE_NAME = k.TABLE_NAME WHERE k.TABLE_SCHEMA = ? AND c.CONSTRAINT_SCHEMA = ? AND k.REFERENCED_COLUMN_NAME IS NOT NULL ORDER BY k.ORDINAL_POSITION","params":{"1":"nominatim","2":"nominatim"},"types":{"1":0,"2":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.633626+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"    SELECT t.TABLE_NAME,\n           t.ENGINE,\n           t.AUTO_INCREMENT,\n           t.TABLE_COMMENT,\n           t.CREATE_OPTIONS,\n           t.TABLE_COLLATION,\n           ccsa.CHARACTER_SET_NAME\n      FROM information_schema.TABLES t\n        INNER JOIN information_schema.COLLATION_CHARACTER_SET_APPLICABILITY ccsa\n          ON ccsa.FULL_COLLATION_NAME = t.TABLE_COLLATION WHERE t.TABLE_SCHEMA = ? AND t.TABLE_TYPE = 'BASE TABLE'","params":{"1":"nominatim"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.633935+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT DATABASE()"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.635568+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT c.TABLE_NAME,       c.COLUMN_NAME        AS field,\n           IF(\n        c.COLUMN_TYPE = 'longtext'\n        AND EXISTS(\n            SELECT * FROM information_schema.CHECK_CONSTRAINTS i_c\n            WHERE i_c.CONSTRAINT_SCHEMA = 'nominatim'\n            AND i_c.TABLE_NAME = c.TABLE_NAME\n            AND i_c.CHECK_CLAUSE = CONCAT(\n                'json_valid(`',\n                    c.COLUMN_NAME,\n                '`)'\n            )\n        ),\n        'json',\n        c.COLUMN_TYPE\n    )       AS type,\n       c.IS_NULLABLE        AS `null`,\n       c.COLUMN_KEY         AS `key`,\n       c.COLUMN_DEFAULT     AS `default`,\n       c.EXTRA,\n       c.COLUMN_COMMENT     AS comment,\n       c.CHARACTER_SET_NAME AS characterset,\n       c.COLLATION_NAME     AS collation\nFROM information_schema.COLUMNS c\n    INNER JOIN information_schema.TABLES t\n        ON t.TABLE_NAME = c.TABLE_NAME WHERE c.TABLE_SCHEMA = ? AND t.TABLE_SCHEMA = ? AND t.TABLE_TYPE = 'BASE TABLE' ORDER BY ORDINAL_POSITION","params":{"1":"nominatim","2":"nominatim"},"types":{"1":0,"2":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.635710+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT TABLE_NAME,        NON_UNIQUE  AS Non_Unique,\n        INDEX_NAME  AS Key_name,\n        COLUMN_NAME AS Column_Name,\n        SUB_PART    AS Sub_Part,\n        INDEX_TYPE  AS Index_Type\nFROM information_schema.STATISTICS WHERE TABLE_SCHEMA = ? ORDER BY SEQ_IN_INDEX","params":{"1":"nominatim"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.636590+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT DISTINCT k.TABLE_NAME,            k.CONSTRAINT_NAME,\n            k.COLUMN_NAME,\n            k.REFERENCED_TABLE_NAME,\n            k.REFERENCED_COLUMN_NAME,\n            k.ORDINAL_POSITION,\n            c.UPDATE_RULE,\n            c.DELETE_RULE\nFROM information_schema.key_column_usage k\nINNER JOIN information_schema.referential_constraints c\nON c.CONSTRAINT_NAME = k.CONSTRAINT_NAME\nAND c.TABLE_NAME = k.TABLE_NAME WHERE k.TABLE_SCHEMA = ? AND c.CONSTRAINT_SCHEMA = ? AND k.REFERENCED_COLUMN_NAME IS NOT NULL ORDER BY k.ORDINAL_POSITION","params":{"1":"nominatim","2":"nominatim"},"types":{"1":0,"2":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.636848+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"    SELECT t.TABLE_NAME,\n           t.ENGINE,\n           t.AUTO_INCREMENT,\n           t.TABLE_COMMENT,\n           t.CREATE_OPTIONS,\n           t.TABLE_COLLATION,\n           ccsa.CHARACTER_SET_NAME\n      FROM information_schema.TABLES t\n        INNER JOIN information_schema.COLLATION_CHARACTER_SET_APPLICABILITY ccsa\n          ON ccsa.FULL_COLLATION_NAME = t.TABLE_COLLATION WHERE t.TABLE_SCHEMA = ? AND t.TABLE_TYPE = 'BASE TABLE'","params":{"1":"nominatim"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.637122+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"ALTER TABLE user ADD created_at DATETIME DEFAULT NULL, ADD updated_at DATETIME DEFAULT NULL"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.639453+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"INSERT INTO doctrine_migration_versions (version, executed_at, execution_time) VALUES (?, ?, ?)","params":{"1":"App\\Infrastructure\\Doctrine\\Migrations\\Version20250630140000","2":"2025-06-30 16:27:00","3":13},"types":{"1":0,"2":0,"3":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:00.644799+02:00","extra":{}}
{"message":"Disconnecting","context":{},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:27:00.649828+02:00","extra":{}}
{"message":"Connecting with parameters {params}","context":{"params":{"driver":"pdo_mysql","host":"maria_db","dbname":"nominatim","user":"nominatim","password":"<redacted>","charset":"utf8mb4"}},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:27:09.061368+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT id_user, username, created_at, updated_at FROM user"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:09.062293+02:00","extra":{}}
{"message":"GET /users - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 16:27:09.059","dateEnd":"2025-06-30 16:27:09.063"},"request":{"method":"GET","path":"/users","params":[],"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"[]"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T16:27:09.063252+02:00","extra":{}}
{"message":"Disconnecting","context":{},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:27:09.063446+02:00","extra":{}}
{"message":"POST /users - 400 Bad Request","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 16:27:18.489","dateEnd":"2025-06-30 16:27:18.491"},"request":{"method":"POST","path":"/users","params":[],"body":null},"response":{"statusCode":400,"reasonPhrase":"Bad Request","body":"{\"error\":\"BAD_REQUEST\",\"description\":\"Bad request\"}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T16:27:18.491227+02:00","extra":{}}
{"message":"Connecting with parameters {params}","context":{"params":{"driver":"pdo_mysql","host":"maria_db","dbname":"nominatim","user":"nominatim","password":"<redacted>","charset":"utf8mb4"}},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:27:27.927930+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT id_user, username, created_at, updated_at FROM user WHERE username = ?","params":{"1":"john.doe"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:27.929737+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"INSERT INTO user (username, password, created_at, updated_at) VALUES (?, ?, ?, ?)","params":{"1":"john.doe","2":"$2y$10$oDcKuQ.0dCCbwusLqOYqruQKutj73csGwEhWirq2IkQcRLpxFhoka","3":"2025-06-30 16:27:27","4":"2025-06-30 16:27:27"},"types":{"1":0,"2":0,"3":0,"4":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:27.978564+02:00","extra":{}}
{"message":"POST /users - 201 Created","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 16:27:27.925","dateEnd":"2025-06-30 16:27:27.979"},"request":{"method":"POST","path":"/users","params":[],"body":{"username":"john.doe","password":"secret123"}},"response":{"statusCode":201,"reasonPhrase":"Created","body":"{\"id\":1,\"username\":\"john.doe\",\"created_at\":\"2025-06-30 16:27:27\",\"updated_at\":\"2025-06-30 16:27:27\"}"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T16:27:27.979624+02:00","extra":{}}
{"message":"Disconnecting","context":{},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:27:27.979773+02:00","extra":{}}
{"message":"Connecting with parameters {params}","context":{"params":{"driver":"pdo_mysql","host":"maria_db","dbname":"nominatim","user":"nominatim","password":"<redacted>","charset":"utf8mb4"}},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:27:46.431219+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT id_user, username, created_at, updated_at FROM user WHERE id_user = ?","params":{"1":1},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:46.432159+02:00","extra":{}}
{"message":"GET /users/1 - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 16:27:46.429","dateEnd":"2025-06-30 16:27:46.432"},"request":{"method":"GET","path":"/users/1","params":[],"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"{\"id\":1,\"username\":\"john.doe\",\"created_at\":\"2025-06-30 16:27:27\",\"updated_at\":\"2025-06-30 16:27:27\"}"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T16:27:46.432709+02:00","extra":{}}
{"message":"Disconnecting","context":{},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:27:46.432857+02:00","extra":{}}
{"message":"Connecting with parameters {params}","context":{"params":{"driver":"pdo_mysql","host":"maria_db","dbname":"nominatim","user":"nominatim","password":"<redacted>","charset":"utf8mb4"}},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:27:54.690126+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT id_user, username, created_at, updated_at FROM user"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:27:54.690998+02:00","extra":{}}
{"message":"GET /users - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 16:27:54.688","dateEnd":"2025-06-30 16:27:54.691"},"request":{"method":"GET","path":"/users","params":[],"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"[{\"id\":1,\"username\":\"john.doe\",\"created_at\":\"2025-06-30 16:27:27\",\"updated_at\":\"2025-06-30 16:27:27\"}]"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T16:27:54.691326+02:00","extra":{}}
{"message":"Disconnecting","context":{},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:27:54.691504+02:00","extra":{}}
{"message":"PUT /users/1 - 400 Bad Request","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 16:28:03.552","dateEnd":"2025-06-30 16:28:03.554"},"request":{"method":"PUT","path":"/users/1","params":[],"body":null},"response":{"statusCode":400,"reasonPhrase":"Bad Request","body":"{\"error\":\"BAD_REQUEST\",\"description\":\"Bad request\"}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T16:28:03.554604+02:00","extra":{}}
{"message":"Connecting with parameters {params}","context":{"params":{"driver":"pdo_mysql","host":"maria_db","dbname":"nominatim","user":"nominatim","password":"<redacted>","charset":"utf8mb4"}},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:28:12.024309+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT id_user, username, created_at, updated_at FROM user WHERE username = ?","params":{"1":"jane.doe"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:28:12.025137+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"INSERT INTO user (username, password, created_at, updated_at) VALUES (?, ?, ?, ?)","params":{"1":"jane.doe","2":"$2y$10$iVJDxLaqscKGFilnwtqnw.dmLvmCarobuG5FgbTjuX2kYNnuFraBO","3":"2025-06-30 16:28:12","4":"2025-06-30 16:28:12"},"types":{"1":0,"2":0,"3":0,"4":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:28:12.073189+02:00","extra":{}}
{"message":"POST /users - 201 Created","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 16:28:12.023","dateEnd":"2025-06-30 16:28:12.074"},"request":{"method":"POST","path":"/users","params":[],"body":{"username":"jane.doe","password":"secret456"}},"response":{"statusCode":201,"reasonPhrase":"Created","body":"{\"id\":2,\"username\":\"jane.doe\",\"created_at\":\"2025-06-30 16:28:12\",\"updated_at\":\"2025-06-30 16:28:12\"}"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T16:28:12.074155+02:00","extra":{}}
{"message":"Disconnecting","context":{},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:28:12.074296+02:00","extra":{}}
{"message":"Connecting with parameters {params}","context":{"params":{"driver":"pdo_mysql","host":"maria_db","dbname":"nominatim","user":"nominatim","password":"<redacted>","charset":"utf8mb4"}},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:28:18.817385+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT id_user, username, created_at, updated_at FROM user WHERE id_user = ?","params":{"1":2},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:28:18.818319+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT id_user, username, created_at, updated_at FROM user WHERE id_user = ?","params":{"1":2},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:28:18.818681+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"DELETE FROM user WHERE id_user = ?","params":{"1":2},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:28:18.818877+02:00","extra":{}}
{"message":"DELETE /users/2 - 204 No Content","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 16:28:18.816","dateEnd":"2025-06-30 16:28:18.819"},"request":{"method":"DELETE","path":"/users/2","params":[],"body":null},"response":{"statusCode":204,"reasonPhrase":"No Content","body":"{\"success\":true}"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T16:28:18.820014+02:00","extra":{}}
{"message":"Disconnecting","context":{},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:28:18.820109+02:00","extra":{}}
{"message":"Connecting with parameters {params}","context":{"params":{"driver":"pdo_mysql","host":"maria_db","dbname":"nominatim","user":"nominatim","password":"<redacted>","charset":"utf8mb4"}},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:28:28.153011+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT id_user, username, created_at, updated_at FROM user"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:28:28.153657+02:00","extra":{}}
{"message":"GET /users - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 16:28:28.152","dateEnd":"2025-06-30 16:28:28.153"},"request":{"method":"GET","path":"/users","params":[],"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"[{\"id\":1,\"username\":\"john.doe\",\"created_at\":\"2025-06-30 16:27:27\",\"updated_at\":\"2025-06-30 16:27:27\"}]"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T16:28:28.153986+02:00","extra":{}}
{"message":"Disconnecting","context":{},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:28:28.154097+02:00","extra":{}}
{"message":"Connecting with parameters {params}","context":{"params":{"driver":"pdo_mysql","host":"maria_db","dbname":"nominatim","user":"nominatim","password":"<redacted>","charset":"utf8mb4"}},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:28:35.128214+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT id_user, username, created_at, updated_at FROM user WHERE id_user = ?","params":{"1":999},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:28:35.129001+02:00","extra":{}}
{"message":"GET /users/999 - 404 Not Found","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 16:28:35.127","dateEnd":"2025-06-30 16:28:35.129"},"request":{"method":"GET","path":"/users/999","params":[],"body":null},"response":{"statusCode":404,"reasonPhrase":"Not Found","body":"{\"error\":\"RESOURCE_NOT_FOUND\",\"description\":\"The user you requested does not exist.\"}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T16:28:35.129434+02:00","extra":{}}
{"message":"Disconnecting","context":{},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:28:35.129552+02:00","extra":{}}
{"message":"Connecting with parameters {params}","context":{"params":{"driver":"pdo_mysql","host":"maria_db","dbname":"nominatim","user":"nominatim","password":"<redacted>","charset":"utf8mb4"}},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:30:07.240493+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT id_user, username, created_at, updated_at FROM user WHERE id_user = ?","params":{"1":1},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:30:07.242646+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT id_user, username, created_at, updated_at FROM user WHERE username = ?","params":{"1":"john.smith"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:30:07.243215+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT id_user, username, created_at, updated_at FROM user WHERE id_user = ?","params":{"1":1},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:30:07.243579+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"UPDATE user SET username = ?, updated_at = ? WHERE id_user = ?","params":{"1":"john.smith","2":"2025-06-30 16:30:07","3":1},"types":{"1":0,"2":0,"3":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:30:07.243751+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT id_user, username, created_at, updated_at FROM user WHERE id_user = ?","params":{"1":1},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:30:07.244412+02:00","extra":{}}
{"message":"PUT /users/1 - 200 OK","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 16:30:07.230","dateEnd":"2025-06-30 16:30:07.244"},"request":{"method":"PUT","path":"/users/1","params":[],"body":null},"response":{"statusCode":200,"reasonPhrase":"OK","body":"{\"id\":1,\"username\":\"john.smith\",\"created_at\":\"2025-06-30 16:27:27\",\"updated_at\":\"2025-06-30 16:30:07\"}"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T16:30:07.244687+02:00","extra":{}}
{"message":"Disconnecting","context":{},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:30:07.244936+02:00","extra":{}}
{"message":"Connecting with parameters {params}","context":{"params":{"driver":"pdo_mysql","host":"maria_db","dbname":"nominatim","user":"nominatim","password":"<redacted>","charset":"utf8mb4"}},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:30:16.306009+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT id_user, username, created_at, updated_at FROM user WHERE username = ?","params":{"1":"alice.wonder"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:30:16.307240+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"INSERT INTO user (username, password, created_at, updated_at) VALUES (?, ?, ?, ?)","params":{"1":"alice.wonder","2":"$2y$10$7IlM0RUNdQYbl/2pU9Maj.Sjamvik9ITv9NVjxxq5.IuHaLOZwSD.","3":"2025-06-30 16:30:16","4":"2025-06-30 16:30:16"},"types":{"1":0,"2":0,"3":0,"4":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T16:30:16.354916+02:00","extra":{}}
{"message":"POST /users - 201 Created","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 16:30:16.303","dateEnd":"2025-06-30 16:30:16.355"},"request":{"method":"POST","path":"/users","params":[],"body":null},"response":{"statusCode":201,"reasonPhrase":"Created","body":"{\"id\":3,\"username\":\"alice.wonder\",\"created_at\":\"2025-06-30 16:30:16\",\"updated_at\":\"2025-06-30 16:30:16\"}"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T16:30:16.355900+02:00","extra":{}}
{"message":"Disconnecting","context":{},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T16:30:16.356040+02:00","extra":{}}
{"message":"Connecting with parameters {params}","context":{"params":{"driver":"pdo_mysql","host":"maria_db","dbname":"nominatim","user":"nominatim","password":"<redacted>","charset":"utf8mb4"}},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T17:06:41.511779+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT DATABASE()"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.512882+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT DATABASE()"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.514232+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT TABLE_NAME\nFROM information_schema.TABLES\nWHERE TABLE_SCHEMA = ?\n  AND TABLE_TYPE = 'BASE TABLE'\nORDER BY TABLE_NAME","params":{"1":"nominatim"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.514520+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT DATABASE()"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.517265+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT       c.COLUMN_NAME        AS field,\n           IF(\n        c.COLUMN_TYPE = 'longtext'\n        AND EXISTS(\n            SELECT * FROM information_schema.CHECK_CONSTRAINTS i_c\n            WHERE i_c.CONSTRAINT_SCHEMA = 'nominatim'\n            AND i_c.TABLE_NAME = c.TABLE_NAME\n            AND i_c.CHECK_CLAUSE = CONCAT(\n                'json_valid(`',\n                    c.COLUMN_NAME,\n                '`)'\n            )\n        ),\n        'json',\n        c.COLUMN_TYPE\n    )       AS type,\n       c.IS_NULLABLE        AS `null`,\n       c.COLUMN_KEY         AS `key`,\n       c.COLUMN_DEFAULT     AS `default`,\n       c.EXTRA,\n       c.COLUMN_COMMENT     AS comment,\n       c.CHARACTER_SET_NAME AS characterset,\n       c.COLLATION_NAME     AS collation\nFROM information_schema.COLUMNS c\n    INNER JOIN information_schema.TABLES t\n        ON t.TABLE_NAME = c.TABLE_NAME WHERE c.TABLE_SCHEMA = ? AND t.TABLE_SCHEMA = ? AND t.TABLE_TYPE = 'BASE TABLE' AND t.TABLE_NAME = ? ORDER BY ORDINAL_POSITION","params":{"1":"nominatim","2":"nominatim","3":"doctrine_migration_versions"},"types":{"1":0,"2":0,"3":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.517451+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT DATABASE()"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.518910+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT        NON_UNIQUE  AS Non_Unique,\n        INDEX_NAME  AS Key_name,\n        COLUMN_NAME AS Column_Name,\n        SUB_PART    AS Sub_Part,\n        INDEX_TYPE  AS Index_Type\nFROM information_schema.STATISTICS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? ORDER BY SEQ_IN_INDEX","params":{"1":"nominatim","2":"doctrine_migration_versions"},"types":{"1":0,"2":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.519017+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT DATABASE()"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.519234+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT DISTINCT            k.CONSTRAINT_NAME,\n            k.COLUMN_NAME,\n            k.REFERENCED_TABLE_NAME,\n            k.REFERENCED_COLUMN_NAME,\n            k.ORDINAL_POSITION,\n            c.UPDATE_RULE,\n            c.DELETE_RULE\nFROM information_schema.key_column_usage k\nINNER JOIN information_schema.referential_constraints c\nON c.CONSTRAINT_NAME = k.CONSTRAINT_NAME\nAND c.TABLE_NAME = k.TABLE_NAME WHERE k.TABLE_SCHEMA = ? AND k.TABLE_NAME = ? AND c.CONSTRAINT_SCHEMA = ? AND k.REFERENCED_COLUMN_NAME IS NOT NULL ORDER BY k.ORDINAL_POSITION","params":{"1":"nominatim","2":"doctrine_migration_versions","3":"nominatim"},"types":{"1":0,"2":0,"3":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.519322+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT DATABASE()"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.519531+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"    SELECT t.TABLE_NAME,\n           t.ENGINE,\n           t.AUTO_INCREMENT,\n           t.TABLE_COMMENT,\n           t.CREATE_OPTIONS,\n           t.TABLE_COLLATION,\n           ccsa.CHARACTER_SET_NAME\n      FROM information_schema.TABLES t\n        INNER JOIN information_schema.COLLATION_CHARACTER_SET_APPLICABILITY ccsa\n          ON ccsa.FULL_COLLATION_NAME = t.TABLE_COLLATION WHERE t.TABLE_SCHEMA = ? AND t.TABLE_NAME = ? AND t.TABLE_TYPE = 'BASE TABLE'","params":{"1":"nominatim","2":"doctrine_migration_versions"},"types":{"1":0,"2":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.519617+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT @@character_set_database, @@collation_database"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.521050+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT * FROM doctrine_migration_versions"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.522836+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT * FROM doctrine_migration_versions"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.523244+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT * FROM doctrine_migration_versions"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.523430+02:00","extra":{}}
{"message":"Beginning transaction","context":{},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.524946+02:00","extra":{}}
{"message":"Executing statement: {sql}","context":{"sql":"SAVEPOINT DOCTRINE_2"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.525271+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT DATABASE()"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.526666+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT c.TABLE_NAME,       c.COLUMN_NAME        AS field,\n           IF(\n        c.COLUMN_TYPE = 'longtext'\n        AND EXISTS(\n            SELECT * FROM information_schema.CHECK_CONSTRAINTS i_c\n            WHERE i_c.CONSTRAINT_SCHEMA = 'nominatim'\n            AND i_c.TABLE_NAME = c.TABLE_NAME\n            AND i_c.CHECK_CLAUSE = CONCAT(\n                'json_valid(`',\n                    c.COLUMN_NAME,\n                '`)'\n            )\n        ),\n        'json',\n        c.COLUMN_TYPE\n    )       AS type,\n       c.IS_NULLABLE        AS `null`,\n       c.COLUMN_KEY         AS `key`,\n       c.COLUMN_DEFAULT     AS `default`,\n       c.EXTRA,\n       c.COLUMN_COMMENT     AS comment,\n       c.CHARACTER_SET_NAME AS characterset,\n       c.COLLATION_NAME     AS collation\nFROM information_schema.COLUMNS c\n    INNER JOIN information_schema.TABLES t\n        ON t.TABLE_NAME = c.TABLE_NAME WHERE c.TABLE_SCHEMA = ? AND t.TABLE_SCHEMA = ? AND t.TABLE_TYPE = 'BASE TABLE' ORDER BY ORDINAL_POSITION","params":{"1":"nominatim","2":"nominatim"},"types":{"1":0,"2":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.526794+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT TABLE_NAME,        NON_UNIQUE  AS Non_Unique,\n        INDEX_NAME  AS Key_name,\n        COLUMN_NAME AS Column_Name,\n        SUB_PART    AS Sub_Part,\n        INDEX_TYPE  AS Index_Type\nFROM information_schema.STATISTICS WHERE TABLE_SCHEMA = ? ORDER BY SEQ_IN_INDEX","params":{"1":"nominatim"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.527679+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT DISTINCT k.TABLE_NAME,            k.CONSTRAINT_NAME,\n            k.COLUMN_NAME,\n            k.REFERENCED_TABLE_NAME,\n            k.REFERENCED_COLUMN_NAME,\n            k.ORDINAL_POSITION,\n            c.UPDATE_RULE,\n            c.DELETE_RULE\nFROM information_schema.key_column_usage k\nINNER JOIN information_schema.referential_constraints c\nON c.CONSTRAINT_NAME = k.CONSTRAINT_NAME\nAND c.TABLE_NAME = k.TABLE_NAME WHERE k.TABLE_SCHEMA = ? AND c.CONSTRAINT_SCHEMA = ? AND k.REFERENCED_COLUMN_NAME IS NOT NULL ORDER BY k.ORDINAL_POSITION","params":{"1":"nominatim","2":"nominatim"},"types":{"1":0,"2":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.527895+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"    SELECT t.TABLE_NAME,\n           t.ENGINE,\n           t.AUTO_INCREMENT,\n           t.TABLE_COMMENT,\n           t.CREATE_OPTIONS,\n           t.TABLE_COLLATION,\n           ccsa.CHARACTER_SET_NAME\n      FROM information_schema.TABLES t\n        INNER JOIN information_schema.COLLATION_CHARACTER_SET_APPLICABILITY ccsa\n          ON ccsa.FULL_COLLATION_NAME = t.TABLE_COLLATION WHERE t.TABLE_SCHEMA = ? AND t.TABLE_TYPE = 'BASE TABLE'","params":{"1":"nominatim"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.528126+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT @@character_set_database, @@collation_database"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.529881+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"SELECT DATABASE()"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.530002+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT c.TABLE_NAME,       c.COLUMN_NAME        AS field,\n           IF(\n        c.COLUMN_TYPE = 'longtext'\n        AND EXISTS(\n            SELECT * FROM information_schema.CHECK_CONSTRAINTS i_c\n            WHERE i_c.CONSTRAINT_SCHEMA = 'nominatim'\n            AND i_c.TABLE_NAME = c.TABLE_NAME\n            AND i_c.CHECK_CLAUSE = CONCAT(\n                'json_valid(`',\n                    c.COLUMN_NAME,\n                '`)'\n            )\n        ),\n        'json',\n        c.COLUMN_TYPE\n    )       AS type,\n       c.IS_NULLABLE        AS `null`,\n       c.COLUMN_KEY         AS `key`,\n       c.COLUMN_DEFAULT     AS `default`,\n       c.EXTRA,\n       c.COLUMN_COMMENT     AS comment,\n       c.CHARACTER_SET_NAME AS characterset,\n       c.COLLATION_NAME     AS collation\nFROM information_schema.COLUMNS c\n    INNER JOIN information_schema.TABLES t\n        ON t.TABLE_NAME = c.TABLE_NAME WHERE c.TABLE_SCHEMA = ? AND t.TABLE_SCHEMA = ? AND t.TABLE_TYPE = 'BASE TABLE' ORDER BY ORDINAL_POSITION","params":{"1":"nominatim","2":"nominatim"},"types":{"1":0,"2":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.530091+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT TABLE_NAME,        NON_UNIQUE  AS Non_Unique,\n        INDEX_NAME  AS Key_name,\n        COLUMN_NAME AS Column_Name,\n        SUB_PART    AS Sub_Part,\n        INDEX_TYPE  AS Index_Type\nFROM information_schema.STATISTICS WHERE TABLE_SCHEMA = ? ORDER BY SEQ_IN_INDEX","params":{"1":"nominatim"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.530858+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT DISTINCT k.TABLE_NAME,            k.CONSTRAINT_NAME,\n            k.COLUMN_NAME,\n            k.REFERENCED_TABLE_NAME,\n            k.REFERENCED_COLUMN_NAME,\n            k.ORDINAL_POSITION,\n            c.UPDATE_RULE,\n            c.DELETE_RULE\nFROM information_schema.key_column_usage k\nINNER JOIN information_schema.referential_constraints c\nON c.CONSTRAINT_NAME = k.CONSTRAINT_NAME\nAND c.TABLE_NAME = k.TABLE_NAME WHERE k.TABLE_SCHEMA = ? AND c.CONSTRAINT_SCHEMA = ? AND k.REFERENCED_COLUMN_NAME IS NOT NULL ORDER BY k.ORDINAL_POSITION","params":{"1":"nominatim","2":"nominatim"},"types":{"1":0,"2":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.531040+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"    SELECT t.TABLE_NAME,\n           t.ENGINE,\n           t.AUTO_INCREMENT,\n           t.TABLE_COMMENT,\n           t.CREATE_OPTIONS,\n           t.TABLE_COLLATION,\n           ccsa.CHARACTER_SET_NAME\n      FROM information_schema.TABLES t\n        INNER JOIN information_schema.COLLATION_CHARACTER_SET_APPLICABILITY ccsa\n          ON ccsa.FULL_COLLATION_NAME = t.TABLE_COLLATION WHERE t.TABLE_SCHEMA = ? AND t.TABLE_TYPE = 'BASE TABLE'","params":{"1":"nominatim"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.531259+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"CREATE TABLE api_tokens (id INT UNSIGNED AUTO_INCREMENT NOT NULL, token VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, user_id INT UNSIGNED NOT NULL, is_active TINYINT(1) DEFAULT 1 NOT NULL, expires_at DATETIME DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, UNIQUE INDEX UNIQ_2CAD560E5F37A13B (token), INDEX IDX_2CAD560EA76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.533405+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"ALTER TABLE api_tokens ADD CONSTRAINT FK_2CAD560EA76ED395 FOREIGN KEY (user_id) REFERENCES user (id_user) ON UPDATE CASCADE ON DELETE CASCADE"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.539295+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"ALTER TABLE access_token DROP FOREIGN KEY FK_B6A2DD686B3CA4B"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.549130+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"DROP TABLE access_token"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.554846+02:00","extra":{}}
{"message":"Executing query: {sql}","context":{"sql":"ALTER TABLE user ADD role VARCHAR(50) DEFAULT 'user' NOT NULL"},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.557514+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"INSERT INTO doctrine_migration_versions (version, executed_at, execution_time) VALUES (?, ?, ?)","params":{"1":"App\\Infrastructure\\Doctrine\\Migrations\\Version20250630150000","2":"2025-06-30 17:06:41","3":38},"types":{"1":0,"2":0,"3":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:41.564121+02:00","extra":{}}
{"message":"Disconnecting","context":{},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T17:06:41.568892+02:00","extra":{}}
{"message":"Authorization header not provided","context":{"message":"Authorization header not provided","file":"/var/www/src/Application/Middleware/Authentication/ApiKeyAuthenticationMiddleware.php","line":46,"trace":"#0 /var/www/src/Application/Middleware/Authentication/ApiKeyAuthenticationMiddleware.php(27): App\\Application\\Middleware\\Authentication\\ApiKeyAuthenticationMiddleware->parseAuthorizationHeader(Object(Nyholm\\Psr7\\ServerRequest))\n#1 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\Authentication\\ApiKeyAuthenticationMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\Route))\n#2 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#3 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#4 /var/www/vendor/slim/slim/Slim/Routing/Route.php(321): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#5 /var/www/vendor/slim/slim/Slim/Routing/RouteRunner.php(74): Slim\\Routing\\Route->run(Object(Nyholm\\Psr7\\ServerRequest))\n#6 /var/www/src/Application/Middleware/ExceptionMiddleware.php(30): Slim\\Routing\\RouteRunner->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#7 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\ExceptionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Slim\\Routing\\RouteRunner))\n#8 /var/www/src/Application/Middleware/LoggerMiddleware.php(31): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#9 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\LoggerMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#10 /var/www/src/Application/Middleware/SessionMiddleware.php(19): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#11 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(177): App\\Application\\Middleware\\SessionMiddleware->process(Object(Nyholm\\Psr7\\ServerRequest), Object(Psr\\Http\\Server\\RequestHandlerInterface@anonymous))\n#12 /var/www/vendor/slim/slim/Slim/MiddlewareDispatcher.php(73): Psr\\Http\\Server\\RequestHandlerInterface@anonymous->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#13 /var/www/vendor/slim/slim/Slim/App.php(209): Slim\\MiddlewareDispatcher->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#14 /var/www/vendor/slim/slim/Slim/App.php(193): Slim\\App->handle(Object(Nyholm\\Psr7\\ServerRequest))\n#15 /var/www/public/index.php(8): Slim\\App->run(Object(Nyholm\\Psr7\\ServerRequest))\n#16 {main}"},"level":400,"level_name":"ERROR","channel":"app","datetime":"2025-06-30T17:06:49.411235+02:00","extra":{}}
{"message":"GET /search - 401 Unauthorized","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 17:06:49.400","dateEnd":"2025-06-30 17:06:49.412"},"request":{"method":"GET","path":"/search","params":{"q":"Paris"},"body":null},"response":{"statusCode":401,"reasonPhrase":"Unauthorized","body":"{\n    \"statusCode\": 401,\n    \"error\": {\n        \"error\": \"UNAUTHENTICATED\",\n        \"description\": \"Authorization header not provided\"\n    }\n}"}},"level":300,"level_name":"WARNING","channel":"app","datetime":"2025-06-30T17:06:49.412229+02:00","extra":{}}
{"message":"Connecting with parameters {params}","context":{"params":{"driver":"pdo_mysql","host":"maria_db","dbname":"nominatim","user":"nominatim","password":"<redacted>","charset":"utf8mb4"}},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T17:06:59.459097+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"SELECT id_user, username, created_at, updated_at, role FROM user WHERE username = ?","params":{"1":"api.user"},"types":{"1":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:59.461167+02:00","extra":{}}
{"message":"Executing statement: {sql} (parameters: {params}, types: {types})","context":{"sql":"INSERT INTO user (username, password, created_at, updated_at) VALUES (?, ?, ?, ?)","params":{"1":"api.user","2":"$2y$10$8VvX5Om/5fm4a6ytbNo8U.PKP7giFTipqxrSIJBcvNyGVfqG3Z/oy","3":"2025-06-30 17:06:59","4":"2025-06-30 17:06:59"},"types":{"1":0,"2":0,"3":0,"4":0}},"level":100,"level_name":"DEBUG","channel":"app","datetime":"2025-06-30T17:06:59.508958+02:00","extra":{}}
{"message":"POST /users - 201 Created","context":{"kpi":{"executionTime":"0.00s","dateStart":"2025-06-30 17:06:59.455","dateEnd":"2025-06-30 17:06:59.510"},"request":{"method":"POST","path":"/users","params":[],"body":null},"response":{"statusCode":201,"reasonPhrase":"Created","body":"{\"id\":4,\"username\":\"api.user\",\"role\":\"user\",\"created_at\":\"2025-06-30 17:06:59\",\"updated_at\":\"2025-06-30 17:06:59\"}"}},"level":250,"level_name":"NOTICE","channel":"app","datetime":"2025-06-30T17:06:59.510129+02:00","extra":{}}
{"message":"Disconnecting","context":{},"level":200,"level_name":"INFO","channel":"app","datetime":"2025-06-30T17:06:59.510311+02:00","extra":{}}
