# Guide : Comment ajouter une nouvelle route dans l'application Slim PHP

Ce guide détaille étape par étape comment ajouter une nouvelle route dans cette application Slim PHP qui suit les principes de l'architecture propre (Clean Architecture).

## Vue d'ensemble de l'architecture

L'application suit une architecture en couches :

1. **Couche Présentation** : Actions (`src/Actions/`)
2. **Couche Domaine** : Cas d'usage et entités (`src/Domain/`)
3. **Couche Infrastructure** : Services externes et persistance (`src/Infrastructure/`)

## Étapes pour ajouter une nouvelle route

### 1. Créer le fichier de route

**Emplacement** : `app/routes/api/[nom-de-votre-route].php`

**Structure** :
```php
<?php

use App\Actions\[Domaine]\[NomAction];
use Slim\App;
use Slim\Interfaces\RouteCollectorProxyInterface as Group;

return function (App $app) {
    $app->get('/votre-endpoint', [NomAction]::class);
    // ou pour un groupe de routes :
    // $app->group('/votre-groupe', function (Group $group) {
    //     $group->get('/endpoint1', [Action1]::class);
    //     $group->post('/endpoint2', [Action2]::class);
    // });
};
```

**Exemple concret** (route de recherche) :
```php
<?php

use App\Actions\Search\SearchAction;
use Slim\App;

return function (App $app) {
    $app->get('/search', SearchAction::class);
};
```

### 2. Créer l'entité du domaine

**Emplacement** : `src/Domain/[NomDomaine]/[NomEntite].php`

**Structure** :
```php
<?php

declare(strict_types=1);

namespace App\Domain\[NomDomaine];

class [NomEntite] implements \JsonSerializable
{
    public function __construct(
        private readonly string $propriete1,
        private readonly int $propriete2,
        // ... autres propriétés
    ) {
    }

    // Getters
    public function getPropriete1(): string
    {
        return $this->propriete1;
    }

    // Implémentation JsonSerializable
    #[\ReturnTypeWillChange]
    public function jsonSerialize(): array
    {
        return [
            'propriete1' => $this->propriete1,
            'propriete2' => $this->propriete2,
        ];
    }
}
```

### 3. Créer les objets Request/Response du cas d'usage

**Emplacement** : `src/Domain/[NomDomaine]/UseCase/[NomCasUsage]/`

**Request** (`[NomCasUsage]Request.php`) :
```php
<?php

namespace App\Domain\[NomDomaine]\UseCase\[NomCasUsage];

class [NomCasUsage]Request
{
    public string $parametre1;
    public int $parametre2 = 10; // valeur par défaut
    // ... autres paramètres
}
```

**Response** (`[NomCasUsage]Response.php`) :
```php
<?php

namespace App\Domain\[NomDomaine]\UseCase\[NomCasUsage];

readonly class [NomCasUsage]Response
{
    public function __construct(
        private array $resultats
    ) {
    }

    public function getResultats(): array
    {
        return $this->resultats;
    }
}
```

### 4. Créer le cas d'usage

**Emplacement** : `src/Domain/[NomDomaine]/UseCase/[NomCasUsage]/[NomCasUsage]UseCase.php`

```php
<?php

namespace App\Domain\[NomDomaine]\UseCase\[NomCasUsage];

use App\Infrastructure\[NomDomaine]\[NomService];

class [NomCasUsage]UseCase
{
    public function __construct(
        private readonly [NomService] $service
    ) {
    }

    public function executer([NomCasUsage]Request $request): [NomCasUsage]Response
    {
        // Logique métier ici
        $resultats = $this->service->faireQuelqueChose($request->parametre1);

        return new [NomCasUsage]Response($resultats);
    }
}
```

### 5. Créer le service d'infrastructure (si nécessaire)

**Emplacement** : `src/Infrastructure/[NomDomaine]/[NomService].php`

```php
<?php

declare(strict_types=1);

namespace App\Infrastructure\[NomDomaine];

use Psr\Log\LoggerInterface;

class [NomService]
{
    public function __construct(
        private readonly LoggerInterface $logger
    ) {
    }

    public function faireQuelqueChose(string $parametre): array
    {
        // Implémentation du service
        // Ex: appel API externe, accès base de données, etc.
        
        $this->logger->info('Service appelé', ['parametre' => $parametre]);
        
        return [];
    }
}
```

### 6. Créer l'Action

**Emplacement** : `src/Actions/[NomDomaine]/[NomAction].php`

```php
<?php

namespace App\Actions\[NomDomaine];

use App\Application\Action\AbstractAction;
use App\Application\Exception\MissingParameterException;
use App\Domain\[NomDomaine]\UseCase\[NomCasUsage]\[NomCasUsage]Request;
use App\Domain\[NomDomaine]\UseCase\[NomCasUsage]\[NomCasUsage]UseCase;
use Psr\Http\Message\ResponseInterface as Response;

class [NomAction] extends AbstractAction
{
    public function __construct(
        private readonly [NomCasUsage]UseCase $useCase
    ) {
    }

    protected function action(): Response
    {
        // Récupération des paramètres
        $params = $this->getParams(); // pour query parameters
        // ou $body = $this->getBody(); // pour body parameters
        // ou $id = $this->resolveArg('id'); // pour route parameters

        // Validation des paramètres requis
        if (empty($params['parametre_requis'])) {
            throw new MissingParameterException('parametre_requis');
        }

        // Création de la requête
        $request = new [NomCasUsage]Request();
        $request->parametre1 = $params['parametre1'];
        $request->parametre2 = isset($params['parametre2']) ? (int) $params['parametre2'] : 10;

        // Exécution du cas d'usage
        $response = $this->useCase->executer($request);

        // Retour de la réponse
        return $this->respondWithData($response->getResultats());
    }
}
```

### 7. Enregistrer les dépendances

**Fichier** : `app/dependencies.php`

Ajouter l'import du service :
```php
use App\Infrastructure\[NomDomaine]\[NomService];
```

Ajouter l'enregistrement dans le tableau de retour :
```php
return [
    // ... autres dépendances
    
    // Vos services
    [NomService]::class => DI\autowire(),
];
```

## Exemple complet : Route de recherche

Voici l'implémentation complète de la route de recherche comme exemple :

### Route (`app/routes/api/search.php`)
```php
<?php

use App\Actions\Search\SearchAction;
use Slim\App;

return function (App $app) {
    $app->get('/search', SearchAction::class);
};
```

### Action (`src/Actions/Search/SearchAction.php`)
```php
<?php

namespace App\Actions\Search;

use App\Application\Action\AbstractAction;
use App\Application\Exception\MissingParameterException;
use App\Domain\Search\UseCase\FreeFormSearch\FreeFormSearchRequest;
use App\Domain\Search\UseCase\FreeFormSearch\FreeFormSearchUseCase;
use Psr\Http\Message\ResponseInterface as Response;

class SearchAction extends AbstractAction
{
    public function __construct(
        private readonly FreeFormSearchUseCase $useCase
    ) {
    }

    protected function action(): Response
    {
        $params = $this->getParams();

        if (empty($params['q'])) {
            throw new MissingParameterException('q');
        }

        $request = new FreeFormSearchRequest();
        $request->query = $params['q'];
        $request->format = $params['format'] ?? 'jsonv2';

        $response = $this->useCase->search($request);

        return $this->respondWithData($response->getResults());
    }
}
```

## Bonnes pratiques

1. **Nommage** : Utilisez des noms explicites et cohérents
2. **Validation** : Validez toujours les paramètres d'entrée
3. **Logging** : Ajoutez des logs pour le débogage
4. **Tests** : Écrivez des tests unitaires pour vos cas d'usage
5. **Documentation** : Documentez vos endpoints API
6. **Gestion d'erreurs** : Utilisez les exceptions appropriées

## Test de votre nouvelle route

1. Démarrez l'application : `php -S localhost:8080 -t public/`
2. Testez avec curl : `curl "http://localhost:8080/votre-endpoint?param=valeur"`
3. Vérifiez les logs dans `logs/`

## Dépannage

- **Erreur 404** : Vérifiez que le fichier de route est dans le bon répertoire
- **Erreur 500** : Vérifiez les logs et la configuration des dépendances
- **Classe non trouvée** : Vérifiez les namespaces et l'autoload Composer
