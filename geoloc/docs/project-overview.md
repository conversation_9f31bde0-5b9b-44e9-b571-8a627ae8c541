# NOMINATIM - Projet de Géolocalisation et Gestion d'API

## Vue d'ensemble du projet

Le projet **NOMINATIM** est une plateforme complète de géolocalisation et de gestion d'API conçue pour fournir des services standardisés et robustes pour les applications nécessitant des fonctionnalités de géolocalisation avancées.

## Vision à long terme

### Objectifs principaux

Le projet vise à fournir des APIs standardisées pour :

1. **🌍 Requêtes de géolocalisation pour adresses**
   - Recherche d'adresses en texte libre
   - Géocodage et géocodage inverse
   - Validation et normalisation d'adresses
   - Support multi-format (JSON, XML, GeoJSON)

2. **⚡ Gestion de files d'attente et traitement asynchrone**
   - Système de queues pour les requêtes volumineuses
   - Traitement en arrière-plan
   - Notifications de statut en temps réel
   - Gestion des priorités

3. **🔄 Traitement synchrone**
   - Réponses en temps réel pour les requêtes simples
   - Cache intelligent pour optimiser les performances
   - Load balancing automatique

4. **🗺️ Requêtes d'itinéraires et de routes**
   - Calcul d'itinéraires optimisés
   - Support multi-modal (voiture, vélo, piéton)
   - Évitement de zones spécifiques
   - Calcul de temps de trajet et distances

5. **📊 Gestion des quotas**
   - Limitation par utilisateur/API key
   - Quotas flexibles (par heure, jour, mois)
   - Monitoring de l'utilisation
   - Alertes de dépassement

6. **🔧 Failover automatique/manuel**
   - Basculement automatique entre services
   - Monitoring de santé des services
   - Configuration de fallback
   - Récupération automatique

7. **🚦 Gestion du rate limiting**
   - Limitation du taux de requêtes
   - Protection contre les abus
   - Throttling intelligent
   - Whitelist/blacklist d'IPs

## Architecture technique

### Principes architecturaux

Le projet suit les principes de **Clean Architecture** avec une séparation claire des responsabilités :

```
┌─────────────────────────────────────────────────────────────┐
│                    Couche Présentation                     │
│                   (Actions/Controllers)                    │
├─────────────────────────────────────────────────────────────┤
│                     Couche Domaine                         │
│                 (Use Cases/Entities)                       │
├─────────────────────────────────────────────────────────────┤
│                  Couche Infrastructure                     │
│              (Repositories/Services)                       │
└─────────────────────────────────────────────────────────────┘
```

### Technologies utilisées

- **Framework** : Slim PHP 4 avec Clean Architecture
- **Base de données** : MariaDB avec Doctrine DBAL
- **Cache** : Redis (prévu)
- **Queue** : RabbitMQ ou Redis Queue (prévu)
- **Containerisation** : Docker & Docker Compose
- **API externe** : Nominatim OpenStreetMap
- **Tests** : PHPUnit
- **Qualité de code** : PHPStan, PHP-CS-Fixer

## État actuel du projet

### ✅ Fonctionnalités implémentées

1. **API de recherche géographique** (`/search`)
   - Recherche en texte libre via Nominatim
   - Support multi-format (json, jsonv2, xml, etc.)
   - Paramètres configurables (limit, addressdetails, etc.)
   - Gestion d'erreurs robuste

2. **API de gestion des utilisateurs** (`/users`)
   - CRUD complet (Create, Read, Update, Delete)
   - Routes RESTful standardisées
   - Validation des données
   - Gestion des erreurs avec codes HTTP appropriés
   - Timestamps automatiques (created_at, updated_at)

3. **Infrastructure de base**
   - Architecture Clean avec séparation des couches
   - Injection de dépendances avec PHP-DI
   - Migrations de base de données avec Doctrine
   - Configuration Docker complète
   - Documentation technique complète

### 🚧 En cours de développement

- Amélioration du parsing JSON pour les requêtes PUT/PATCH
- Tests unitaires et d'intégration
- Authentification et autorisation
- Logging et monitoring

### 📋 Roadmap

#### Phase 1 - Fondations (En cours)
- [x] API de recherche géographique
- [x] API de gestion des utilisateurs
- [x] Architecture Clean
- [ ] Authentification JWT
- [ ] Tests complets

#### Phase 2 - Fonctionnalités avancées
- [ ] Système de quotas
- [ ] Rate limiting
- [ ] Cache Redis
- [ ] API d'itinéraires

#### Phase 3 - Scalabilité
- [ ] Système de queues
- [ ] Traitement asynchrone
- [ ] Failover automatique
- [ ] Monitoring avancé

#### Phase 4 - Optimisation
- [ ] Load balancing
- [ ] Optimisations de performance
- [ ] Analytics et reporting
- [ ] Interface d'administration

## Structure du projet

```
geoloc/
├── src/
│   ├── Actions/           # Couche présentation (HTTP handlers)
│   ├── Domain/            # Couche domaine (business logic)
│   └── Infrastructure/    # Couche infrastructure (external services)
├── app/
│   ├── routes/           # Définition des routes
│   ├── dependencies.php # Configuration DI
│   └── settings.php     # Configuration application
├── docs/                # Documentation
├── tests/               # Tests unitaires et d'intégration
└── docker/              # Configuration Docker
```

## APIs disponibles

### 🔍 API de recherche géographique

```bash
GET /search?q={query}&format={format}&limit={limit}
```

**Exemple** :
```bash
curl "http://localhost:8080/search?q=144%20rue%20odin&format=jsonv2"
```

### 👥 API de gestion des utilisateurs

```bash
GET    /users           # Liste tous les utilisateurs
POST   /users           # Crée un nouvel utilisateur
GET    /users/{id}      # Récupère un utilisateur spécifique
PUT    /users/{id}      # Met à jour un utilisateur
DELETE /users/{id}      # Supprime un utilisateur
```

**Exemples** :
```bash
# Créer un utilisateur
curl -X POST "http://localhost:8080/users" \
  -d "username=john.doe&password=secret123"

# Lister les utilisateurs
curl "http://localhost:8080/users"

# Supprimer un utilisateur
curl -X DELETE "http://localhost:8080/users/1"
```

## Contribution

Pour contribuer au projet, suivez le guide détaillé dans `docs/ajouter-nouvelle-route.md` qui explique comment ajouter de nouvelles fonctionnalités en respectant l'architecture établie.

## Déploiement

Le projet utilise Docker pour un déploiement simplifié :

```bash
# Démarrer l'environnement complet
docker compose up -d

# Exécuter les migrations
docker exec app-skeleton php vendor/bin/doctrine-migrations migrate

# L'API est disponible sur http://localhost:8080
```

---

*Ce document sera mis à jour au fur et à mesure de l'évolution du projet.*
