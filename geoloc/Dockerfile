FROM php:8.3-apache AS builder

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Build and configure apache server
RUN apt-get -y update \
    && apt-get install -y --no-install-recommends  \
        locales \
        apt-utils \
        sudo \
        wget \
        libicu-dev \
        libfreetype6-dev \
        libjpeg62-turbo-dev \
        libpng-dev \
        libzip-dev \
        libssh2-1-dev \
        libxrender1 \
        libxtst6 \
        libfontconfig1 \
        git \
        zip \
        unzip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/

# Setup language + format
RUN echo "fr_FR.UTF-8 UTF-8" >> /etc/locale.gen && locale-gen

# Enable apache rewrite + headers
RUN a2enmod rewrite && a2enmod headers

# Install required php extensions
RUN docker-php-ext-install \
    bcmath \
    intl \
    pdo_mysql \
    zip \
    gd

# Install libssh2
RUN pecl install ssh2-1.3.1 \
    && docker-php-ext-enable ssh2

# Set working directory (root) as /var/www
WORKDIR /var/www

# Copy composer files
COPY composer.json composer.lock* ./

# Install composer dependencies
RUN composer install --no-dev --optimize-autoloader --no-interaction


FROM builder AS development

# Install + enable xdebug extension
RUN pecl install xdebug \
    && docker-php-ext-enable xdebug

# Install phpunit
RUN wget -O phpunit https://phar.phpunit.de/phpunit-10.phar \
    && mv phpunit /usr/local/bin/phpunit \
    && chmod +x /usr/local/bin/phpunit

# Copy composer files for development dependencies
COPY composer.json composer.lock* ./

# Install all composer dependencies (including dev dependencies)
RUN composer install --optimize-autoloader --no-interaction

# Copy vendor to a backup location
RUN cp -r vendor /tmp/vendor-backup

ARG USER_ID
ARG GROUP_ID

# Create user sync with host user (for windows permissions issues)
RUN if getent passwd www-data ; then userdel www-data; fi &&\
    if getent group www-data ; then groupdel www-data; fi &&\
    groupadd -g ${GROUP_ID} www-data &&\
    useradd -l -u ${USER_ID} -g www-data www-data &&\
    install -d -m 0755 -o www-data -g www-data /home/<USER>

# Create initialization script
RUN echo '#!/bin/bash\n\
# Wait a moment for volume mount to complete\n\
sleep 1\n\
if [ ! -d "/var/www/vendor" ] || [ -z "$(ls -A /var/www/vendor 2>/dev/null)" ]; then\n\
    echo "Initializing vendor directory..."\n\
    mkdir -p /var/www/vendor\n\
    if [ -d "/tmp/vendor-backup" ]; then\n\
        cp -r /tmp/vendor-backup/* /var/www/vendor/\n\
        echo "Vendor directory initialized successfully"\n\
    else\n\
        echo "Warning: vendor backup not found, running composer install..."\n\
        cd /var/www && composer install --no-dev --optimize-autoloader --no-interaction\n\
    fi\n\
    chown -R www-data:www-data /var/www/vendor\n\
else\n\
    echo "Vendor directory already exists"\n\
fi\n\
# Fix Apache log permissions\n\
chown -R www-data:www-data /var/log/apache2\n\
# Execute the command\n\
exec "$@"' > /usr/local/bin/init-vendor.sh && chmod +x /usr/local/bin/init-vendor.sh

# Set the entrypoint to initialize vendor before starting apache
ENTRYPOINT ["/usr/local/bin/init-vendor.sh"]
CMD ["apache2-foreground"]

