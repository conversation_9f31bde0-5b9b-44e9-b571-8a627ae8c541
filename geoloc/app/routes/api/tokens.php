<?php

use App\Actions\ApiToken\CreateTokenAction;
use App\Actions\ApiToken\DeleteTokenAction;
use App\Actions\ApiToken\ListUserTokensAction;
use App\Actions\ApiToken\UpdateTokenAction;
use App\Application\Middleware\Authentication\ApiKeyAuthenticationMiddleware;
use Slim\App;
use Slim\Interfaces\RouteCollectorProxyInterface as Group;

return function (App $app) {
    $app->group('/users/{id}/tokens', function (Group $group) {
        $group->get('', ListUserTokensAction::class);                    // GET /users/{id}/tokens
        $group->post('', CreateTokenAction::class);                      // POST /users/{id}/tokens
        $group->put('/{tokenId}', UpdateTokenAction::class);             // PUT /users/{id}/tokens/{tokenId}
        $group->delete('/{tokenId}', DeleteTokenAction::class);          // DELETE /users/{id}/tokens/{tokenId}
    })->add(ApiKeyAuthenticationMiddleware::class);
};
