<?php

use App\Actions\User\CreateUserAction;
use App\Actions\User\DeleteUserAction;
use App\Actions\User\ListUsersAction;
use App\Actions\User\UpdateUserAction;
use App\Actions\User\ViewUserAction;
use Slim\App;
use Slim\Interfaces\RouteCollectorProxyInterface as Group;

return function (App $app) {
    $app->group('/users', function (Group $group) {
        $group->get('', ListUsersAction::class);           // GET /users
        $group->post('', CreateUserAction::class);         // POST /users
        $group->get('/{id}', ViewUserAction::class);       // GET /users/{id}
        $group->put('/{id}', UpdateUserAction::class);     // PUT /users/{id}
        $group->delete('/{id}', DeleteUserAction::class);  // DELETE /users/{id}
    });
};
