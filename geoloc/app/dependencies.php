<?php

/**
 * Define and configure external dependencies (libraries like Logger) for DI.
 *
 * @example LoggerInterface::class => DI\factory(Monolog\Logger::class)
 */

use App\Application\ApplicationFilesystemInterface;
use App\Application\Handlers\ShutdownHandler;
use App\Application\Middleware\ExceptionMiddleware;
use App\Infrastructure\Cache\SessionCache;
use App\Infrastructure\Search\NominatimSearchService;
use App\Domain\ApiToken\ApiTokenRepository;
use App\Infrastructure\Persistence\Domain\ApiToken\DoctrineApiTokenRepository;
use App\Infrastructure\Persistence\Application\AccessToken\DoctrineAccessTokenRepository;
use App\Infrastructure\Persistence\Application\AuthCode\DoctrineAuthCodeRepository;
use App\Infrastructure\Persistence\Application\Client\ClientRepository;
use App\Infrastructure\Persistence\Application\RefreshToken\DoctrineRefreshTokenRepository;
use App\Infrastructure\Persistence\Application\Scope\ScopeRepository;
use App\Infrastructure\Persistence\Application\User\UserRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\DriverManager;
use League\Flysystem\Filesystem;
use League\Flysystem\Local\LocalFilesystemAdapter;
use League\OAuth2\Server\Repositories\AccessTokenRepositoryInterface;
use League\OAuth2\Server\Repositories\AuthCodeRepositoryInterface;
use League\OAuth2\Server\Repositories\ClientRepositoryInterface;
use League\OAuth2\Server\Repositories\RefreshTokenRepositoryInterface;
use League\OAuth2\Server\Repositories\ScopeRepositoryInterface;
use League\OAuth2\Server\Repositories\UserRepositoryInterface;
use Nyholm\Psr7\Factory\Psr17Factory;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseFactoryInterface;
use Psr\Http\Message\ServerRequestFactoryInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Message\StreamFactoryInterface;
use Psr\Http\Message\UploadedFileFactoryInterface;
use Psr\Http\Message\UriFactoryInterface;
use Psr\Log\LoggerInterface;
use Psr\SimpleCache\CacheInterface;
use Slim\Factory\ServerRequestCreatorFactory;

return [
    LoggerInterface::class => DI\factory(
        function (ContainerInterface $container, App\Infrastructure\Monolog\LoggerFactory $factory) {
            return $factory->createApplicationLogger();
        }
    ),

    Connection::class => DI\factory(
        function (ContainerInterface $container) {

            $params = [
                'driver' => $_ENV['DB_DRIVER'],
                'host' => $_ENV['DB_HOST'],
                'dbname' => $_ENV['DB_NAME'],
                'user' => $_ENV['DB_USER'],
                'password' => $_ENV['DB_PASSWORD'],
                'charset' => 'utf8mb4',
            ];

            $configuration = new Doctrine\DBAL\Configuration();
            $configuration->setMiddlewares(
                [
                    $container->get(Doctrine\DBAL\Logging\Middleware::class),
                ]
            );

            return DriverManager::getConnection($params, $configuration);
        }
    ),

    CacheInterface::class => DI\autowire(SessionCache::class),

    ApplicationFilesystemInterface::class => DI\factory(
        function (ContainerInterface $container) {
            $adapter = new LocalFilesystemAdapter(__DIR__ . '/../');

            return new Filesystem($adapter);
        }
    ),

    // Psr-7 HTTP factories (with Nyholm package)
    ResponseFactoryInterface::class => function (ContainerInterface $container) {
        return $container->get(Psr17Factory::class);
    },
    ServerRequestFactoryInterface::class => function (ContainerInterface $container) {
        return $container->get(Psr17Factory::class);
    },
    StreamFactoryInterface::class => function (ContainerInterface $container) {
        return $container->get(Psr17Factory::class);
    },
    UploadedFileFactoryInterface::class => function (ContainerInterface $container) {
        return $container->get(Psr17Factory::class);
    },
    UriFactoryInterface::class => function (ContainerInterface $container) {
        return $container->get(Psr17Factory::class);
    },

    ServerRequestInterface::class => DI\factory(
        function (ContainerInterface $container) {
            $serverRequestCreator = ServerRequestCreatorFactory::create();

            return $serverRequestCreator->createServerRequestFromGlobals();
        }
    ),

    ShutdownHandler::class => DI\autowire()
        ->constructorParameter('displayErrorDetails', 'dev' === strtolower($_ENV['APP_ENV'])),

    ExceptionMiddleware::class => DI\autowire()
        ->constructorParameter('displayErrorDetails', 'dev' === strtolower($_ENV['APP_ENV'])),

    // OAuth2 interfaces
    AccessTokenRepositoryInterface::class => DI\autowire(DoctrineAccessTokenRepository::class),
    AuthCodeRepositoryInterface::class => DI\autowire(DoctrineAuthCodeRepository::class),
    ClientRepositoryInterface::class => DI\autowire(ClientRepository::class),
    RefreshTokenRepositoryInterface::class => DI\autowire(DoctrineRefreshTokenRepository::class),
    ScopeRepositoryInterface::class => DI\autowire(ScopeRepository::class),
    UserRepositoryInterface::class => DI\autowire(UserRepository::class),

    // OAuth 2

    // Search services
    NominatimSearchService::class => DI\autowire(),
];
