services:
    app:
        container_name: app-skeleton
        build:
            context: geoloc
            target: development
            args:
                USER_ID: 1000
                GROUP_ID: 1000
        restart: "no"
        env_file: geoloc/.env
        ports:
            - "8080:80"
        volumes:
            - ./geoloc/.docker/vhosts:/etc/apache2/sites-enabled
            - ./geoloc/.docker/httpd.conf:/etc/apache2/conf-enabled/z-docker-logs.conf
            - ./geoloc/.docker/php.ini:/usr/local/etc/php/conf.d/php.ini
            - ./geoloc/.docker/xdebug.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
            - ./geoloc:/var/www
        networks:
            - default
        links:
            - maria_db:mysql
        depends_on:
            - maria_db
        deploy:
            resources:
                limits:
                    cpus: '2'
                    memory: 512M

    maria_db:
        container_name: app-mariadb_10-11
        image: mariadb:10.11
        restart: "no"
        env_file: geoloc/.env
        environment:
            MARIADB_DATABASE: ${DB_NAME}
            MARIADB_USER: ${DB_USER}
            MARIADB_PASSWORD: ${DB_PASSWORD}
            MARIADB_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
        ports:
            - "3306:3306"
        volumes:
            - geoloc_db_data:/var/lib/mysql
        networks:
            - default
        deploy:
            resources:
                limits:
                    cpus: '2'
                    memory: 512M

    phpmyadmin:
        container_name: app-phpmyadmin_5
        image: phpmyadmin:5
        restart: "no"
        env_file: geoloc/.env
        environment:
            PMA_HOST: mysql
            UPLOAD_LIMIT: 64M
        ports:
            - "8001:80"
        links:
            - maria_db:mysql
        depends_on:
            - maria_db
        deploy:
            resources:
                limits:
                    cpus: '1'
                    memory: 256M


    nominatim:
        container_name: nominatim
        image: mediagis/nominatim:5.1
        ports:
            - "8090:8080"
        environment:
            # see https://github.com/mediagis/nominatim-docker/tree/master/5.1#configuration for more options
            PBF_URL: https://download.geofabrik.de/europe/france/languedoc-roussillon-latest.osm.pbf
            REPLICATION_URL: https://download.geofabrik.de/europe/france/languedoc-roussillon-updates/
            NOMINATIM_PASSWORD: very_secure_password
        volumes:
            - nominatim_data:/var/lib/postgresql/16/main
        shm_size: 1gb
        networks:
            - default
        deploy:
            resources:
                limits:
                    cpus: '2'
                    memory: 4G

volumes:
    geoloc_db_data:
    nominatim_data: